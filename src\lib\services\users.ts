import sql from '$lib/db/db.js';
import type { User } from '$lib/types/tables.js';

export async function saveUser(user: User): Promise<User> {
  if (user.user_id) {
    const [updatedUser] = await sql`
      UPDATE users
      SET user_firstname = ${user.user_firstname},
          user_email = ${user.user_email},
          user_created_at = ${user.user_created_at},
          user_type = ${user.user_type}
      WHERE user_id = ${user.user_id}
      RETURNING *
    `;
    return updatedUser as User;
  } else {
    const [newUser] = await sql`
      INSERT INTO users (user_firstname, user_email, user_created_at, user_type)
      VALUES (${user.user_firstname}, ${user.user_email}, ${user.user_created_at}, ${user.user_type})
      RETURNING *
    `;
    return newUser as User;
  }
}

export async function deleteUser(user_id: number): Promise<boolean> {
  const result = await sql`
    DELETE FROM users WHERE user_id = ${user_id}
  `;
  return result.count > 0;
}

export async function getUsers(limit = 50, offset = 0): Promise<{ users: User[]; total: number }> {
  try {
    const users = await sql`
      SELECT * FROM users
      ORDER BY user_firstname
      LIMIT ${limit} OFFSET ${offset}
    `;

    const [countResult] = await sql`
      SELECT COUNT(*) as count FROM users
    `;

    return { users: users as unknown as User[], total: Number(countResult.count) };
  } catch (error: any) {
    // Handle case where table doesn't exist
    if (error?.code === '42P01') { // PostgreSQL error code for "relation does not exist"
      throw new Error('Users table does not exist. Please run database setup first.');
    }
    throw error;
  }
}

export async function getUserById(user_id: number): Promise<User | null> {
  const [user] = await sql`
    SELECT * FROM users WHERE user_id = ${user_id}
  `;
  return (user as User) || null;
}

export async function getUserByEmail(email: string): Promise<User | null> {
  const [user] = await sql`
    SELECT * FROM users WHERE user_email = ${email}
  `;
  return (user as User) || null;
}