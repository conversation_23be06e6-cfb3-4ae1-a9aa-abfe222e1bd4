import { getItemsWithMetadata } from '$lib/services/items.js';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url }) => {
	try {
		// Get page from URL params
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = 40;
		const offset = (page - 1) * limit;

		// Get featured items with metadata
		const { items, total } = await getItemsWithMetadata('featured', limit, offset);

		// Calculate pagination info
		const totalPages = Math.ceil(total / limit);
		const hasNextPage = page < totalPages;
		const hasPrevPage = page > 1;

		return {
			items,
			total,
			currentPage: page,
			totalPages,
			hasNextPage,
			hasPrevPage
		};
	} catch (error) {
		console.error('Error loading featured data:', error);
		return {
			items: [],
			total: 0,
			currentPage: 1,
			totalPages: 0,
			hasNextPage: false,
			hasPrevPage: false
		};
	}
};
