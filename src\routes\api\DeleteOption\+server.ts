import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { deleteOption } from '$lib/services/options.js';
import { authenticateRequest, createUnauthorizedResponse, createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { DeleteOptionRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    if (!authenticateRequest(request)) {
      return json(createUnauthorizedResponse(), { status: 401 });
    }
    
    const body: DeleteOptionRequest = await request.json();
    const deleted = await deleteOption(body.option_id);
    
    return json(createSuccessResponse({ deleted }));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
