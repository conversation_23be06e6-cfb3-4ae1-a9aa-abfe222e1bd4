import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { analyzeDatabase } from '$lib/services/preparedb.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { AnalyzeDatabaseRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    console.log('AnalyzeDatabase API called');
    const body: AnalyzeDatabaseRequest = await request.json();
    console.log('Request body:', body);
    
    if (!body.sqlite_file) {
      console.log('Missing sqlite_file parameter');
      return json(createErrorResponse('sqlite_file is required'), { status: 400 });
    }
    
    console.log('Starting database analysis for:', body.sqlite_file);
    const result = await analyzeDatabase(body.sqlite_file);
    console.log('Analysis completed:', result);
    
    return json(createSuccessResponse(result));
  } catch (error) {
    console.error('AnalyzeDatabase API error:', error);
    return json(createErrorResponse(error), { status: 500 });
  }
};
