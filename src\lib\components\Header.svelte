<script lang="ts">
	import { Menu, X, LogOut, User } from 'lucide-svelte';
	import { authStore } from '$lib/stores/auth';
	import { onMount } from 'svelte';
	import { PUBLIC_SITE_NAME } from '$lib/utils/env';

	let mobileMenuOpen = $state(false);
	let userMenuOpen = $state(false);
	let authState = $state({ isAuthenticated: false, user: null as any, loading: true });

	onMount(() => {
		const unsubscribe = authStore.subscribe(auth => {
			authState = auth;
		});

		// Close user menu when clicking outside
		const handleClickOutside = (event: MouseEvent) => {
			const target = event.target as Element;
			if (userMenuOpen && !target.closest('.user-menu-container')) {
				userMenuOpen = false;
			}
		};

		document.addEventListener('click', handleClickOutside);

		return () => {
			unsubscribe();
			document.removeEventListener('click', handleClickOutside);
		};
	});

	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen;
	}

	function toggleUserMenu() {
		userMenuOpen = !userMenuOpen;
	}

	function handleLogout() {
		authStore.logout();
		mobileMenuOpen = false;
		userMenuOpen = false;
	}
</script>

<header class="bg-white py-4 shadow-sm sticky top-0 z-50 border-b border-gray-100">
	<div class="container mx-auto px-4 sm:px-6 flex justify-between items-center">
		<!-- Logo -->
		<a href="/" class="flex items-center space-x-3 group">
			<img 
				src="/assets/images/logo.svg" 
				alt="{PUBLIC_SITE_NAME} logo" 
				class="h-9 w-9 rounded-lg shadow-sm group-hover:scale-105 transition-transform"
				width="40"
				height="40"
				decoding="async"
			>
			<span class="text-xl font-bold text-blue-900">{PUBLIC_SITE_NAME}</span>
		</a>
		
		<!-- Desktop Navigation -->
		<nav class="hidden md:flex items-center space-x-7">
			<a href="/new" class="text-gray-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200">
				Discover
			</a>
			<a href="/categories" class="text-gray-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200">
				Categories
			</a>
			<a href="/submit" class="text-gray-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200">
				Submit a Tool
			</a>
		</nav>

		<!-- Desktop Auth Buttons -->
		<div class="hidden md:flex items-center space-x-4">
			{#if !authState.loading}
				{#if authState.isAuthenticated}
					<!-- User Menu -->
					<div class="relative user-menu-container">
						<button
							onclick={toggleUserMenu}
							class="bg-blue-900 text-white rounded-full p-2 hover:bg-blue-800 transition-colors"
							aria-label="User menu"
						>
							<User class="h-5 w-5" />
						</button>

						{#if userMenuOpen}
							<div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50">
								<div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
									Hello, {authState.user?.user_firstname}
								</div>
								<button onclick={handleLogout} class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
									<LogOut class="w-4 h-4 mr-2 inline" />
									Logout
								</button>
							</div>
						{/if}
					</div>
				{:else}
					<a href="/login" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium">
						Login
					</a>
					<a href="/signup" class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium">
						Sign Up
					</a>
				{/if}
			{/if}
		</div>

		<!-- Mobile Menu Button -->
		<div class="md:hidden">
			<button
				onclick={toggleMobileMenu}
				class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
				aria-label="Open menu"
			>
				{#if mobileMenuOpen}
					<X class="h-6 w-6" />
				{:else}
					<Menu class="h-6 w-6" />
				{/if}
			</button>
		</div>
	</div>
	
	<!-- Mobile Menu -->
	{#if mobileMenuOpen}
		<div class="md:hidden mt-4 bg-white rounded-lg shadow-sm py-2 px-4 border border-gray-100">
			<a href="/new" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
				Discover
			</a>
			<a href="/categories" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
				Categories
			</a>
			<a href="/submit" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
				Submit a Tool
			</a>
			{#if !authState.loading}
				{#if authState.isAuthenticated}
					<div class="block py-2 px-4 text-sm text-gray-600 border-t border-gray-200 mt-2 pt-2">
						Hello, {authState.user?.user_firstname}
					</div>
					<button onclick={handleLogout} class="block w-full text-left py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
						<LogOut class="w-4 h-4 mr-2 inline" />
						Logout
					</button>
				{:else}
					<div class="border-t border-gray-200 mt-2 pt-2">
						<a href="/login" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
							Login
						</a>
						<a href="/signup" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
							Sign Up
						</a>
					</div>
				{/if}
			{/if}
		</div>
	{/if}
</header>
