import jwt from 'jsonwebtoken';

const JWT_SECRET = 'your-secret-key-here';
const MASTER_PASSWORD = 'asdf1234';

export interface UserJwtPayload {
  user_id: number;
  user_email: string;
  user_firstname: string;
  user_type: number;
  type: 'user';
}

export interface MasterJwtPayload {
  type: 'master';
  authenticated: boolean;
}

export function generateMasterToken(): string {
  const payload: MasterJwtPayload = {
    type: 'master',
    authenticated: true
  };
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '24h' });
}

export function verifyMasterToken(token: string): MasterJwtPayload | null {
  try {
    const payload = jwt.verify(token, JWT_SECRET) as MasterJwtPayload;
    return payload.type === 'master' && payload.authenticated ? payload : null;
  } catch (error) {
    return null;
  }
}

export function validateMasterPassword(password: string): boolean {
  return password === MASTER_PASSWORD;
}

export function generateUserToken(payload: UserJwtPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' });
}

export function verifyUserToken(token: string): UserJwtPayload | null {
  try {
    const payload = jwt.verify(token, JWT_SECRET) as UserJwtPayload;
    return payload.type === 'user' ? payload : null;
  } catch (error) {
    return null;
  }
}


