import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { saveItemMeta } from '$lib/services/item_metas.js';
import { authenticateRequest, createUnauthorizedResponse, createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { SaveItemMetaRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    if (!authenticateRequest(request)) {
      return json(createUnauthorizedResponse(), { status: 401 });
    }
    
    const body: SaveItemMetaRequest = await request.json();
    const itemMeta = await saveItemMeta(body.item_meta);
    
    return json(createSuccessResponse(itemMeta));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
