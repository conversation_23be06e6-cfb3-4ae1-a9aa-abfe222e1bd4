import sql from '$lib/db/db.js';
import type { Term } from '$lib/types/tables.js';

export async function saveTerm(term: Term): Promise<Term> {
  if (term.term_id) {
    const [updatedTerm] = await sql`
      UPDATE terms
      SET term_name = ${term.term_name},
          term_slug = ${term.term_slug},
          term_taxonomy = ${term.term_taxonomy}
      WHERE term_id = ${term.term_id}
      RETURNING *
    `;
    return updatedTerm as Term;
  } else {
    const [newTerm] = await sql`
      INSERT INTO terms (term_name, term_slug, term_taxonomy)
      VALUES (${term.term_name}, ${term.term_slug}, ${term.term_taxonomy})
      RETURNING *
    `;
    return newTerm as Term;
  }
}

export async function deleteTerm(term_id: number): Promise<boolean> {
  const result = await sql`
    DELETE FROM terms WHERE term_id = ${term_id}
  `;
  return result.count > 0;
}

export async function getTerms(term_taxonomy?: string): Promise<Term[]> {
  let terms;

  if (term_taxonomy !== undefined) {
    terms = await sql`
      SELECT * FROM terms
      WHERE term_taxonomy = ${term_taxonomy}
      ORDER BY term_name
    `;
  } else {
    terms = await sql`
      SELECT * FROM terms
      ORDER BY term_name
    `;
  }

  return terms as unknown as Term[];
}

export async function getTermsWithMetas(term_taxonomy?: string): Promise<any[]> {
  let terms;

  if (term_taxonomy !== undefined) {
    terms = await sql`
      SELECT
        t.*,
        tm.term_meta_key,
        tm.term_meta_value
      FROM terms t
      LEFT JOIN term_metas tm ON t.term_id = tm.term_id
      WHERE t.term_taxonomy = ${term_taxonomy}
      ORDER BY t.term_name, tm.term_meta_key
    `;
  } else {
    terms = await sql`
      SELECT
        t.*,
        tm.term_meta_key,
        tm.term_meta_value
      FROM terms t
      LEFT JOIN term_metas tm ON t.term_id = tm.term_id
      ORDER BY t.term_name, tm.term_meta_key
    `;
  }

  // Group the results by term_id to create enriched term objects
  const termMap = new Map();

  for (const row of terms) {
    if (!termMap.has(row.term_id)) {
      termMap.set(row.term_id, {
        term_id: row.term_id,
        term_name: row.term_name,
        term_slug: row.term_slug,
        term_taxonomy: row.term_taxonomy,
        metadata: []
      });
    }

    if (row.term_meta_key) {
      termMap.get(row.term_id).metadata.push({
        term_meta_key: row.term_meta_key,
        term_meta_value: row.term_meta_value
      });
    }
  }

  return Array.from(termMap.values());
}

export async function getTermById(term_id: number): Promise<Term | null> {
  const [term] = await sql`
    SELECT * FROM terms WHERE term_id = ${term_id}
  `;
  return (term as Term) || null;
}