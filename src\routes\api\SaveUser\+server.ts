import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { saveUser } from '$lib/services/users.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { SaveUserRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: SaveUserRequest = await request.json();
    const user = await saveUser(body.user);

    return json(createSuccessResponse(user));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
