import type { User, Item, Term, ItemMeta, TermMeta, Option } from './tables.js';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface SaveUserResponse extends ApiResponse<User> {}
export interface DeleteUserResponse extends ApiResponse<{ deleted: boolean }> {}
export interface GetUsersResponse extends ApiResponse<{ users: User[]; total: number }> {}

export interface SaveItemResponse extends ApiResponse<Item> {}
export interface DeleteItemResponse extends ApiResponse<{ deleted: boolean }> {}
export interface GetItemsResponse extends ApiResponse<{ items: Item[]; total: number }> {}

export interface SaveTermResponse extends ApiResponse<Term> {}
export interface DeleteTermResponse extends ApiResponse<{ deleted: boolean }> {}
export interface GetTermsResponse extends ApiResponse<{ terms: Term[]; total: number }> {}

export interface SaveItemMetaResponse extends ApiResponse<ItemMeta> {}
export interface DeleteItemMetaResponse extends ApiResponse<{ deleted: boolean }> {}
export interface GetItemMetasResponse extends ApiResponse<{ item_metas: ItemMeta[]; total: number }> {}

export interface SaveTermMetaResponse extends ApiResponse<TermMeta> {}
export interface DeleteTermMetaResponse extends ApiResponse<{ deleted: boolean }> {}
export interface GetTermMetasResponse extends ApiResponse<{ term_metas: TermMeta[]; total: number }> {}

export interface SaveOptionResponse extends ApiResponse<Option> {}
export interface DeleteOptionResponse extends ApiResponse<{ deleted: boolean }> {}

export interface GetSqliteFilesResponse extends ApiResponse<{ files: string[] }> {}
export interface MigrateDatabaseResponse extends ApiResponse<{
  success: boolean;
  message: string;
  stats: {
    users_migrated: number;
    items_migrated: number;
    terms_migrated: number;
    item_metas_migrated: number;
    term_metas_migrated: number;
    relationships_migrated: number;
  }
}> {}
export interface CleanDatabaseResponse extends ApiResponse<{ cleaned: boolean; message: string }> {}

export interface AnalyzeDatabaseResponse extends ApiResponse<{
  total_users: number;
  admin_users: number;
  member_users: number;
  total_tools: number;
  admin_tools: number;
  member_tools: number;
  total_relationships: number;
  admin_relationships: number;
}> {}

export interface RemoveAdminToolsResponse extends ApiResponse<{
  success: boolean;
  message: string;
  stats: {
    tools_removed: number;
    relationships_removed: number;
  }
}> {}
export interface GetOptionsResponse extends ApiResponse<{ options: Option[]; total: number }> {}

export interface LoginResponse extends ApiResponse<{ token: string }> {}

export interface GoogleAuthResponse extends ApiResponse<{
  token: string;
  user: {
    user_id: number;
    user_firstname: string;
    user_email: string;
    user_type: number;
  }
}> {}