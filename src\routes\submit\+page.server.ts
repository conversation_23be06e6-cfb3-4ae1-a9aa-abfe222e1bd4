import { getTerms } from '$lib/services/terms.js';
import { verifyUserToken } from '$lib/services/auth.js';
import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ cookies }) => {
	const token = cookies.get('auth_token');

	if (!token) {
		throw redirect(302, '/login?redirect=/submit');
	}

	const userPayload = verifyUserToken(token);
	if (!userPayload) {
		cookies.delete('auth_token', { path: '/' });
		throw redirect(302, '/login?redirect=/submit');
	}

	try {
		// Get categories and pricing terms
		const categories = await getTerms('category');
		const pricingOptions = await getTerms('pricing');

		return {
			categories,
			pricingOptions,
			user: {
				user_id: userPayload.user_id,
				user_firstname: userPayload.user_firstname,
				user_email: userPayload.user_email,
				user_role: userPayload.user_role
			}
		};
	} catch (error) {
		console.error('Error loading submit page data:', error);
		return {
			categories: [],
			pricingOptions: [],
			user: {
				user_id: userPayload.user_id,
				user_firstname: userPayload.user_firstname,
				user_email: userPayload.user_email,
				user_role: userPayload.user_role
			}
		};
	}
};
