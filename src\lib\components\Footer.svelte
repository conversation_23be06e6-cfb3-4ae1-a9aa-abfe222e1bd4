<script lang="ts">
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_DESCRIPTION } from '$lib/utils/env';
</script>

<footer class="bg-blue-900 text-white">
	<div class="container mx-auto py-12 lg:py-16 px-4 sm:px-6">
		<div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8">
			<!-- Logo and Description -->
			<div class="col-span-2 lg:col-span-3">
				<div class="flex items-center space-x-3 mb-4">
					<img
						src="/assets/images/icon.svg"
						alt="{PUBLIC_SITE_NAME} logo"
						class="h-9 w-9 rounded-lg"
						width="40"
						height="40"
						loading="lazy"
						decoding="async"
					>
					<span class="text-xl font-bold">{PUBLIC_SITE_NAME}</span>
				</div>
				<p class="text-sm text-gray-300 mb-4 max-w-sm">
					{PUBLIC_SITE_DESCRIPTION}
				</p>
				<!-- Social Links -->
				<div class="flex items-center space-x-4">
					<a 
						href="https://x.com" 
						target="_blank" 
						rel="noopener noreferrer"
						class="text-gray-300 hover:text-white transition-colors"
						aria-label="Follow us on X (Twitter)"
					>
						<svg class="w-5 h-5" viewBox="0 0 500 470" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
							<path d="M1.21951 0L194.284 258.928L0 469.388H43.75L213.796 285.073L351.22 469.388H500L296.113 195.935L476.905 0H433.232L276.601 169.713L150.076 0H1.21951ZM65.5488 32.2608H133.918L435.747 437.05H367.378L65.5488 32.2608Z"/>
						</svg>
					</a>
				</div>
			</div>

			<!-- Site link -->
			<div>
				<h4 class="font-semibold mb-4 text-sm text-white">Site link</h4>
				<ul class="space-y-2 text-sm text-gray-300">
					<li>
						<a href="/categories" class="hover:underline hover:text-white transition-colors">
							Categories
						</a>
					</li>
					<li>
						<a href="/submit" class="hover:underline hover:text-white transition-colors">
							Submit
						</a>
					</li>
				</ul>
			</div>

			<!-- Legal -->
			<div>
				<h4 class="font-semibold mb-4 text-sm text-white">Legal</h4>
				<ul class="space-y-2 text-sm text-gray-300">
					<li>
						<a href="/terms" class="hover:underline hover:text-white transition-colors">
							Terms & Conditions
						</a>
					</li>
					<li>
						<a href="/privacy" class="hover:underline hover:text-white transition-colors">
							Privacy Policy
						</a>
					</li>
				</ul>
			</div>
		</div>
		
		<!-- Copyright -->
		<div class="border-t border-blue-800 mt-10 pt-6 text-center text-sm text-gray-400">
			<p>&copy; 2025 AI Directory. All Rights Reserved.</p>
		</div>
	</div>
</footer>
