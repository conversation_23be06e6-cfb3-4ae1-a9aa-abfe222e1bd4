<script lang="ts">
	import Head from '$lib/components/Head.svelte';
	import { Check, X, ExternalLink, Star, Plus, Minus } from 'lucide-svelte';
	import { PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// State for featured tools management
	let selectedActiveItem = $state('');
	let isManagingFeatured = $state(false);

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Handle approval/rejection
	async function handleItemAction(itemId: number, action: 'approve' | 'reject') {
		try {
			const response = await fetch('/api/ManageItem', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					itemId,
					action
				})
			});

			const result = await response.json();

			if (response.ok) {
				// Reload the page to refresh the list
				window.location.reload();
			} else {
				alert(result.error || 'Error processing request');
			}
		} catch (error) {
			console.error('Error:', error);
			alert('Error processing request');
		}
	}

	// Handle featured tool management
	async function handleFeaturedAction(itemId: number, action: 'add' | 'remove') {
		isManagingFeatured = true;
		try {
			const response = await fetch('/api/ManageFeaturedTool', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					itemId,
					action
				})
			});

			const result = await response.json();

			if (response.ok) {
				// Reload the page to refresh the list
				window.location.reload();
			} else {
				alert(result.error || 'Error processing request');
			}
		} catch (error) {
			console.error('Error:', error);
			alert('Error processing request');
		} finally {
			isManagingFeatured = false;
		}
	}

	// Add selected item to featured
	async function addToFeatured() {
		if (!selectedActiveItem) {
			alert('Please select a tool to add to featured');
			return;
		}
		await handleFeaturedAction(parseInt(selectedActiveItem), 'add');
		selectedActiveItem = '';
	}


</script>

<Head
	title="Dashboard - Manage Tools"
	description="Manage pending AI tool submissions and featured tools"
	url="{PUBLIC_SITE_URL}/dashboard"
/>

<div class="container mx-auto px-4 sm:px-6 py-8">
	<div class="mb-8">
		<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-6">Dashboard</h1>
		<p class="text-lg text-gray-600">
			Review and manage pending AI tool submissions and featured tools.
		</p>
	</div>

	<!-- Featured Tools Management Section -->
	<div class="mb-12 bg-white border border-gray-200 shadow-sm rounded-lg">
		<div class="p-6 border-b border-gray-100">
			<div class="flex items-center gap-3">
				<Star class="w-6 h-6 text-orange-500 fill-current" />
				<h2 class="text-2xl font-bold text-blue-900">Featured Tools Management</h2>
			</div>
			<p class="text-gray-600 mt-2">Add or remove tools from the featured section.</p>
		</div>

		<div class="p-6">
			<!-- Add to Featured Section -->
			<div class="mb-8">
				<h3 class="text-lg font-semibold text-gray-700 mb-4">Add Tool to Featured</h3>
				<div class="flex gap-4 items-end">
					<div class="flex-1">
						<label for="activeItemSelect" class="block text-sm font-medium text-gray-700 mb-2">
							Select Active Tool
						</label>
						<select
							id="activeItemSelect"
							bind:value={selectedActiveItem}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
						>
							<option value="">Choose a tool...</option>
							{#each data.activeItems.filter(item => item.item_status === 1) as item}
								<option value={item.item_id}>{item.item_name}</option>
							{/each}
						</select>
					</div>
					<button
						onclick={addToFeatured}
						disabled={isManagingFeatured || !selectedActiveItem}
						class="px-4 py-2 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 text-white rounded-md transition-colors flex items-center gap-2"
					>
						<Plus class="w-4 h-4" />
						Add to Featured
					</button>
				</div>
			</div>

			<!-- Current Featured Tools -->
			<div>
				<h3 class="text-lg font-semibold text-gray-700 mb-4">Current Featured Tools ({data.featuredItems.length})</h3>
				{#if data.featuredItems.length === 0}
					<p class="text-gray-500 italic">No featured tools yet.</p>
				{:else}
					<div class="space-y-3">
						{#each data.featuredItems as item}
							<div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
								<div class="flex items-center gap-4">
									<Star class="w-5 h-5 text-orange-500 fill-current" />
									<div>
										<h4 class="font-semibold text-blue-900">{item.item_name}</h4>
										<p class="text-sm text-gray-600">{getMetaValue(item, 'description') || 'AI tool for productivity'}</p>
										<a href={item.item_url} target="_blank" rel="noopener noreferrer" class="text-xs text-blue-600 hover:underline inline-flex items-center gap-1">
											{item.item_url}
											<ExternalLink class="w-3 h-3" />
										</a>
									</div>
								</div>
								<button
									onclick={() => handleFeaturedAction(item.item_id, 'remove')}
									disabled={isManagingFeatured}
									class="px-3 py-2 text-red-600 border border-red-300 rounded-md hover:bg-red-50 disabled:bg-gray-100 transition-colors flex items-center gap-2"
								>
									<Minus class="w-4 h-4" />
									Remove
								</button>
							</div>
						{/each}
					</div>
				{/if}
			</div>
		</div>
	</div>

	<!-- Pending Items Section -->
	<div class="mb-8">
		<h2 class="text-2xl font-bold text-blue-900 mb-6">Pending Submissions ({data.total})</h2>

		{#if data.items.length === 0}
			<div class="text-center py-12 bg-white border border-gray-200 rounded-lg shadow-sm">
				<div class="p-6">
					<h3 class="text-xl font-semibold text-gray-600 mb-4">No Pending Items</h3>
					<p class="text-gray-500">All submissions have been reviewed.</p>
				</div>
			</div>
		{:else}
			<!-- Items List -->
		<div class="space-y-6 mb-12">
			{#each data.items as item}
				<div class="bg-white border border-gray-200 shadow-sm rounded-lg">
					<div class="p-6 border-b border-gray-100">
						<div class="flex justify-between items-start">
							<div>
								<h3 class="text-xl text-blue-900 font-semibold">{item.item_name}</h3>
								<p class="text-gray-600 mt-1">{getMetaValue(item, 'description')}</p>
							</div>
							<span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
								Pending
							</span>
						</div>
					</div>
					<div class="p-6">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h4 class="font-semibold text-gray-700 mb-2">Details</h4>
								<div class="space-y-2 text-sm">
									<div><strong>Website:</strong>
										<a href={item.item_url} target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline inline-flex items-center gap-1">
											{item.item_url}
											<ExternalLink class="w-3 h-3" />
										</a>
									</div>
									<div><strong>Submitted:</strong> {new Date(item.item_created_at * 1000).toLocaleDateString()}</div>
								</div>
							</div>
							<div>
								<h4 class="font-semibold text-gray-700 mb-2">Description</h4>
								<p class="text-sm text-gray-600 line-clamp-3">{getMetaValue(item, 'content')}</p>
							</div>
						</div>

						<div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-100">
							<button
								onclick={() => handleItemAction(item.item_id, 'reject')}
								class="px-4 py-2 text-red-600 border border-red-300 rounded-md hover:bg-red-50 transition-colors flex items-center gap-2"
							>
								<X class="w-4 h-4" />
								Reject
							</button>
							<button
								onclick={() => handleItemAction(item.item_id, 'approve')}
								class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors flex items-center gap-2"
							>
								<Check class="w-4 h-4" />
								Approve
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>

			<!-- Total Info -->
			<div class="text-center mt-8 text-sm text-gray-600">
				Showing {data.total} pending items total
			</div>
		{/if}
	</div>
</div>
