import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { deleteItemMeta } from '$lib/services/item_metas.js';
import { authenticateRequest, createUnauthorizedResponse, createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { DeleteItemMetaRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    if (!authenticateRequest(request)) {
      return json(createUnauthorizedResponse(), { status: 401 });
    }
    
    const body: DeleteItemMetaRequest = await request.json();
    const deleted = await deleteItemMeta(body.item_meta_id);
    
    return json(createSuccessResponse({ deleted }));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
