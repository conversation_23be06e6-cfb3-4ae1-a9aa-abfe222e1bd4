import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export interface AuthUser {
  user_id: number;
  user_firstname: string;
  user_email: string;
  user_type: number;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  token: string | null;
  loading: boolean;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: true
};

function createAuthStore() {
  const { subscribe, set, update } = writable<AuthState>(initialState);

  return {
    subscribe,

    init: (user?: AuthUser) => {
      if (user) {
        set({
          isAuthenticated: true,
          user,
          token: null,
          loading: false
        });
      } else {
        set({ ...initialState, loading: false });
      }
    },

    login: (token: string, user: AuthUser) => {
      set({
        isAuthenticated: true,
        user,
        token,
        loading: false
      });
    },

    logout: async () => {
      if (browser) {
        try {
          await fetch('/api/UserLogout', { method: 'POST' });
        } catch (error) {
          console.error('Logout error:', error);
        }
      }
      set({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false
      });
    },

    setLoading: (loading: boolean) => {
      update(state => ({ ...state, loading }));
    }
  };
}

export const authStore = createAuthStore();
