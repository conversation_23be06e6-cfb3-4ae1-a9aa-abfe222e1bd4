<script lang="ts">
	import { goto } from '$app/navigation';
	import Head from '$lib/components/Head.svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import { authStore } from '$lib/stores/auth';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	let isLoading = $state(false);
	let error = $state('');

	onMount(() => {
		const unsubscribe = authStore.subscribe(auth => {
			if (auth.isAuthenticated && !auth.loading) {
				goto('/');
			}
		});

		// Check for error in URL params
		const urlParams = new URLSearchParams(window.location.search);
		const urlError = urlParams.get('error');
		if (urlError) {
			switch (urlError) {
				case 'missing_code':
					error = 'Authentication failed. Please try again.';
					break;
				case 'email_not_verified':
					error = 'Your Google email is not verified. Please verify your email and try again.';
					break;
				case 'account_inactive':
					error = 'Your account is inactive. Please contact support.';
					break;
				case 'authentication_failed':
					error = 'Authentication failed. Please try again.';
					break;
				default:
					error = 'An error occurred during signup. Please try again.';
			}
		}

		return unsubscribe;
	});

	function handleGoogleSignup() {
		isLoading = true;
		error = '';

		// Redirect to Google OAuth
		const redirectTo = encodeURIComponent(data.redirectTo);
		window.location.href = `/api/auth/google?redirectTo=${redirectTo}`;
	}
</script>

<Head
	title="Sign Up - {PUBLIC_SITE_NAME}"
	description="Create your account to submit and manage your AI tools."
	url="{PUBLIC_SITE_URL}/signup"
/>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
	<div class="max-w-md w-full">
		<div class="bg-white shadow-lg rounded-lg">
			<div class="space-y-1 py-6 px-6 border-b border-gray-100">
				<h1 class="text-3xl font-bold text-center text-blue-900">Join Us</h1>
				<p class="text-center text-gray-600">
					Sign up with your Google account
				</p>
			</div>
			<div class="space-y-6 px-6 pb-6 pt-6">
				{#if error}
					<div class="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
						{error}
					</div>
				{/if}

				<button
					onclick={handleGoogleSignup}
					disabled={isLoading}
					class="w-full bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 px-6 py-3 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
				>
					<div class="flex items-center justify-center space-x-3">
						<svg class="w-5 h-5" viewBox="0 0 24 24">
							<path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
							<path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
							<path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
							<path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
						</svg>
						<span>{isLoading ? 'Creating Account...' : 'Continue with Google'}</span>
					</div>
				</button>

				<div class="text-center text-sm text-gray-600">
					Already have an account?
					<a href="/login" class="text-blue-600 hover:text-blue-800 font-medium">
						Sign in here
					</a>
				</div>
			</div>
		</div>
	</div>
</div>
