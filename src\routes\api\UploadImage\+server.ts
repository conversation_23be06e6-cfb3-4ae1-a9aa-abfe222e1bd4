import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export const POST: RequestHandler = async ({ request }) => {
	try {
		console.log('Upload request received');
		const formData = await request.formData();
		const file = formData.get('file') as File;
		const type = formData.get('type') as string; // 'screenshot' or 'icon'

		console.log('File info:', { name: file?.name, size: file?.size, type: file?.type, uploadType: type });

		if (!file) {
			return json({ error: 'No file provided' }, { status: 400 });
		}

		// Validate file type
		if (!file.type.startsWith('image/')) {
			return json({ error: 'File must be an image' }, { status: 400 });
		}

		// Validate file size (reduced limits to avoid timeout)
		const maxSize = type === 'screenshot' ? 2 * 1024 * 1024 : 1 * 1024 * 1024; // 2MB for screenshot, 1MB for icon
		if (file.size > maxSize) {
			return json({ error: `File size must be less than ${maxSize / (1024 * 1024)}MB` }, { status: 400 });
		}

		// Create images directory if it doesn't exist
		const imagesDir = path.join(process.cwd(), 'static', 'images');
		if (!existsSync(imagesDir)) {
			await mkdir(imagesDir, { recursive: true });
		}

		// Generate unique filename
		const timestamp = Date.now();
		const randomString = Math.random().toString(36).substring(2, 15);
		const extension = path.extname(file.name);
		const filename = `${type}_${timestamp}_${randomString}${extension}`;
		const filepath = path.join(imagesDir, filename);

		console.log('Converting file to buffer...');
		// Convert file to buffer
		const arrayBuffer = await file.arrayBuffer();
		const buffer = Buffer.from(arrayBuffer);

		console.log('Writing original file to disk (no processing for now)...');
		// For now, just save the original file to test basic upload
		await writeFile(filepath, buffer);

		// TODO: Add Jimp processing back once basic upload works
		// console.log('Loading Jimp...');
		// const { Jimp } = await import('jimp');
		// console.log('Reading image with Jimp...');
		// const image = await Jimp.read(buffer);
		// ... processing code ...

		console.log('Upload completed successfully');
		// Return the URL path
		const imageUrl = `/images/${filename}`;

		return json({
			success: true,
			imageUrl,
			filename,
			originalName: file.name,
			size: file.size
			// TODO: Add dimensions back when Jimp processing is restored
			// dimensions: {
			//   width: image.getWidth(),
			//   height: image.getHeight()
			// }
		});

	} catch (error) {
		console.error('Upload error:', error);
		return json({ error: 'Failed to upload image' }, { status: 500 });
	}
};
