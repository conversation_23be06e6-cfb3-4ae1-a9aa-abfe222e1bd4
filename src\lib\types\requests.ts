import type { User, Item, Term, ItemMeta, TermMeta, Option } from './tables.js';

export interface SaveUserRequest {
  user: User;
}

export interface DeleteUserRequest {
  user_id: number;
}

export interface GetUsersRequest {
  limit?: number;
  offset?: number;
}

export interface SaveItemRequest {
  item: Item;
}

export interface DeleteItemRequest {
  item_id: number;
}

export interface GetItemsRequest {
  user_id?: number;
  limit?: number;
  offset?: number;
}

export interface SaveTermRequest {
  term: Term;
}

export interface DeleteTermRequest {
  term_id: number;
}

export interface GetTermsRequest {
  term_taxonomy?: string;
  limit?: number;
  offset?: number;
}

export interface SaveItemMetaRequest {
  item_meta: ItemMeta;
}

export interface DeleteItemMetaRequest {
  item_meta_id: number;
}

export interface GetItemMetasRequest {
  item_id?: number;
  limit?: number;
  offset?: number;
}

export interface SaveTermMetaRequest {
  term_meta: TermMeta;
}

export interface DeleteTermMetaRequest {
  term_meta_id: number;
}

export interface GetTermMetasRequest {
  term_id?: number;
  limit?: number;
  offset?: number;
}

export interface SaveOptionRequest {
  option: Option;
}

export interface DeleteOptionRequest {
  option_id: number;
}

export interface GetSqliteFilesRequest {}

export interface MigrateDatabaseRequest {
  sqlite_file: string;
}

export interface CleanDatabaseRequest {}

export interface AnalyzeDatabaseRequest {
  sqlite_file: string;
}

export interface RemoveAdminToolsRequest {
  sqlite_file: string;
}

export interface GetOptionsRequest {
  limit?: number;
  offset?: number;
}

export interface LoginRequest {
  password: string;
}

export interface GoogleAuthRequest {
  code: string;
  state?: string;
}