import { getTerms } from '$lib/services/terms.js';
import type { PageServerLoad } from './$types';
import sql from '$lib/db/db.js';

export const load: PageServerLoad = async () => {
	try {
		// Get all categories
		const categories = await getTerms('category');

		// Get item counts for each category (only active or featured items)
		const categoriesWithCounts = await Promise.all(
			categories.map(async (category: any) => {
				const [countResult] = await sql`
					SELECT COUNT(DISTINCT i.item_id) as count
					FROM items i
					JOIN item_term_relationships itr ON i.item_id = itr.item_id
					JOIN terms t ON itr.term_id = t.term_id
					WHERE (i.item_status = 1 OR i.item_status = 2)
						AND t.term_id = ${category.term_id}
				`;
				return {
					...category,
					item_count: Number(countResult.count)
				};
			})
		);

		return {
			categories: categoriesWithCounts
		};
	} catch (error) {
		console.error('Error loading categories data:', error);
		return {
			categories: []
		};
	}
};
