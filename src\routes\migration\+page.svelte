<script lang="ts">
  import { onMount } from 'svelte';
  import Head from '$lib/components/Head.svelte';
  import { PUBLIC_SITE_URL } from '$lib/utils/env';
  import type { GetSqliteFilesResponse, MigrateDatabaseResponse, CleanDatabaseResponse } from '$lib/types/responses.js';

  let sqliteFiles: string[] = [];
  let selectedSqliteFile: string = '';
  let isLoading = false;
  let isCleaning = false;
  let message = '';
  let messageType: 'success' | 'error' | 'info' = 'info';
  let migrationStats: any = null;

  onMount(async () => {
    await loadSqliteFiles();
  });

  async function loadSqliteFiles() {
    try {
      const response = await fetch('/api/GetSqliteFiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
      
      const result: GetSqliteFilesResponse = await response.json();
      if (result.success && result.data) {
        sqliteFiles = result.data.files;
      }
    } catch (error) {
      console.error('Error loading SQLite files:', error);
      showMessage('Failed to load SQLite files', 'error');
    }
  }

  async function migrateDatabase() {
    if (!selectedSqliteFile) {
      showMessage('Please select both a site and a SQLite file', 'error');
      return;
    }

    isLoading = true;
    migrationStats = null;

    try {
      console.log('Starting migration with:', { sqlite_file: selectedSqliteFile });

      const response = await fetch('/api/MigrateDatabase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sqlite_file: selectedSqliteFile
        })
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: MigrateDatabaseResponse = await response.json();
      console.log('Migration result:', result);

      if (result.success && result.data) {
        migrationStats = result.data.stats;
        showMessage(result.data.message, 'success');
      } else {
        showMessage(result.error || 'Migration failed', 'error');
      }
    } catch (error) {
      console.error('Migration error:', error);
      showMessage('Migration failed: ' + (error instanceof Error ? error.message : 'Unknown error'), 'error');
    } finally {
      isLoading = false;
    }
  }

  async function cleanDatabase() {
    if (!confirm('Are you sure you want to clean the database? This will remove all data and cannot be undone.')) {
      return;
    }

    isCleaning = true;
    
    try {
      const response = await fetch('/api/CleanDatabase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
      
      const result: CleanDatabaseResponse = await response.json();
      if (result.success && result.data) {
        showMessage(result.data.message, 'success');
        migrationStats = null;
      } else {
        showMessage(result.error || 'Clean failed', 'error');
      }
    } catch (error) {
      console.error('Clean error:', error);
      showMessage('Clean failed: ' + (error instanceof Error ? error.message : 'Unknown error'), 'error');
    } finally {
      isCleaning = false;
    }
  }

  function showMessage(msg: string, type: 'success' | 'error' | 'info') {
    message = msg;
    messageType = type;
    setTimeout(() => {
      message = '';
    }, 5000);
  }
</script>

<Head
  title="Database Migration"
  description="Database migration tool for AI Tools Directory"
  url="{PUBLIC_SITE_URL}/migration"
/>

<div class="container mx-auto px-4 py-8 max-w-4xl">
  <h1 class="text-3xl font-bold mb-8 text-center">Database Migration</h1>
  
  {#if message}
    <div class="mb-6 p-4 rounded-lg {messageType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : messageType === 'error' ? 'bg-red-100 text-red-800 border border-red-200' : 'bg-blue-100 text-blue-800 border border-blue-200'}">
      {message}
    </div>
  {/if}

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Migration Form -->
    <div class="bg-white p-6 rounded-lg shadow-md border">
      <h2 class="text-xl font-semibold mb-4">Migrate SQLite Database</h2>
      
      <div class="space-y-4">
        <div>
          <label for="file-select" class="block text-sm font-medium text-gray-700 mb-2">
            Select SQLite Database File
          </label>
          <select
            id="file-select"
            bind:value={selectedSqliteFile}
            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isLoading}
          >
            <option value="">Choose a database file...</option>
            {#each sqliteFiles as file}
              <option value={file}>{file}</option>
            {/each}
          </select>
        </div>

        <button
          onclick={migrateDatabase}
          disabled={isLoading || !selectedSqliteFile}
          class="w-full px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {#if isLoading}
            <span class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Migrating...
            </span>
          {:else}
            Start Migration
          {/if}
        </button>
      </div>
    </div>

    <!-- Clean Database -->
    <div class="bg-white p-6 rounded-lg shadow-md border">
      <h2 class="text-xl font-semibold mb-4 text-red-600">Clean Database</h2>
      
      <p class="text-gray-600 mb-4">
        This will remove all data from the PostgreSQL database.
        Use this to start fresh before a new migration.
      </p>
      
      <button
        onclick={cleanDatabase}
        disabled={isCleaning}
        class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        {#if isCleaning}
          <span class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Cleaning...
          </span>
        {:else}
          Clean Database
        {/if}
      </button>
    </div>
  </div>

  <!-- Migration Statistics -->
  {#if migrationStats}
    <div class="mt-8 bg-white p-6 rounded-lg shadow-md border">
      <h2 class="text-xl font-semibold mb-4">Migration Statistics</h2>
      
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
          <div class="text-2xl font-bold text-blue-600">{migrationStats.users_migrated}</div>
          <div class="text-sm text-gray-600">Users</div>
        </div>
        
        <div class="text-center p-4 bg-green-50 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{migrationStats.items_migrated}</div>
          <div class="text-sm text-gray-600">Items</div>
        </div>
        
        <div class="text-center p-4 bg-purple-50 rounded-lg">
          <div class="text-2xl font-bold text-purple-600">{migrationStats.terms_migrated}</div>
          <div class="text-sm text-gray-600">Terms</div>
        </div>
        
        <div class="text-center p-4 bg-yellow-50 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600">{migrationStats.item_metas_migrated}</div>
          <div class="text-sm text-gray-600">Item Metas</div>
        </div>
        
        <div class="text-center p-4 bg-indigo-50 rounded-lg">
          <div class="text-2xl font-bold text-indigo-600">{migrationStats.term_metas_migrated}</div>
          <div class="text-sm text-gray-600">Term Metas</div>
        </div>
        
        <div class="text-center p-4 bg-pink-50 rounded-lg">
          <div class="text-2xl font-bold text-pink-600">{migrationStats.relationships_migrated}</div>
          <div class="text-sm text-gray-600">Relationships</div>
        </div>
      </div>
    </div>
  {/if}
</div>
