import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getSqliteFiles } from '$lib/services/migration.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    console.log('GetSqliteFiles API called');
    const files = await getSqliteFiles();
    console.log('Found SQLite files:', files);

    return json(createSuccessResponse({ files }));
  } catch (error) {
    console.error('Error in GetSqliteFiles:', error);
    return json(createErrorResponse(error), { status: 500 });
  }
};
