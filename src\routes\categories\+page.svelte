<script lang="ts">
	import Head from '$lib/components/Head.svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();
</script>

<Head
	title="All Categories - {PUBLIC_SITE_NAME}"
	description="All Categories to find AI websites and tools."
	url="{PUBLIC_SITE_URL}/categories"
/>

<div class="container mx-auto py-12 px-4 sm:px-6">
	<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-6 text-center">Find AI By Category</h1>
	<p class="text-lg text-gray-600 mb-12 text-center">Browse categories to find AI websites and tools.</p>

	<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
		{#each data.categories as category}
			<a href="/category/{category.term_slug}" class="block">
				<div class="bg-white p-5 rounded-sm border border-gray-200 shadow-sm hover:border-blue-900 hover:shadow-md hover:-translate-y-1 transition-all duration-300 ease-in-out">
					<div class="flex justify-between items-center">
						<h3 class="font-semibold text-lg text-blue-900">{category.term_name}</h3>
						<span class="bg-gray-100 text-gray-600 text-xs font-medium px-2 py-0.5 rounded-full">
							{category.item_count || 0} tools
						</span>
					</div>
				</div>
			</a>
		{/each}
	</div>
</div>
