<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  // Import tab components
  import UsersTab from './UsersTab.svelte';
  import ItemsTab from './ItemsTab.svelte';
  import TermsTab from './TermsTab.svelte';
  import OptionsTab from './OptionsTab.svelte';
  import ItemMetasTab from './ItemMetasTab.svelte';
  import TermMetasTab from './TermMetasTab.svelte';

  import type { User, Item, Term, Option, ItemMeta, TermMeta } from '$lib/types/tables.js';

  // Authentication state
  let isAuthenticated = false;
  let password = '';
  let token = '';
  let loginError = '';

  // Data arrays
  let users: User[] = [];
  let items: Item[] = [];
  let terms: Term[] = [];
  let options: Option[] = [];
  let itemMetas: ItemMeta[] = [];
  let termMetas: TermMeta[] = [];

  let loading = false;
  let activeTab = 'users';
  let dataError = '';
  let showSetupMessage = false;

  // API helper function
  async function apiCall(endpoint: string, data?: any) {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  // Authentication functions
  async function login() {
    try {
      loginError = '';
      const response = await fetch('/api/Login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      });

      const result = await response.json();
      console.log('Login response:', result);

      if (result.success) {
        token = result.data.token;
        isAuthenticated = true;
        if (browser) {
          localStorage.setItem('master_token', token);
        }
        await loadData();
      } else {
        loginError = result.error || 'Login failed';
      }
    } catch (error) {
      console.error('Login error:', error);
      loginError = 'Login failed. Please try again.';
    }
  }

  function logout() {
    isAuthenticated = false;
    token = '';
    password = '';
    if (browser) {
      localStorage.removeItem('master_token');
    }
  }

  // Data loading function
  async function loadData() {
    try {
      loading = true;
      dataError = '';

      const [usersRes, itemsRes, termsRes, optionsRes, itemMetasRes, termMetasRes] = await Promise.all([
        apiCall('/api/GetUsers'),
        apiCall('/api/GetItems'),
        apiCall('/api/GetTerms'),
        apiCall('/api/GetOptions'),
        apiCall('/api/GetItemMetas'),
        apiCall('/api/GetTermMetas')
      ]);

      console.log('API Responses:', { usersRes, itemsRes, termsRes, optionsRes, itemMetasRes, termMetasRes });

      users = usersRes.data?.users || [];
      items = itemsRes.data?.items || [];
      terms = termsRes.data?.terms || [];
      options = optionsRes.data?.options || [];
      itemMetas = itemMetasRes.data?.item_metas || [];
      termMetas = termMetasRes.data?.term_metas || [];

      console.log('Parsed data:', { users: users.length, items: items.length, terms: terms.length, options: options.length, itemMetas: itemMetas.length, termMetas: termMetas.length });

      showSetupMessage = users.length === 0;
    } catch (error) {
      console.error('Failed to load data:', error);
      dataError = 'Failed to load data. Please try again.';
    } finally {
      loading = false;
    }
  }



  // Tab switching function
  function switchTab(newTab: string) {
    activeTab = newTab;
  }

  // Initialize on mount
  onMount(async () => {
    if (browser) {
      const savedToken = localStorage.getItem('master_token');
      if (savedToken) {
        token = savedToken;
        isAuthenticated = true;
        await loadData();
      }
    }
  });
</script>

<svelte:head>
  <title>Master Dashboard</title>
</svelte:head>

<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    {#if !isAuthenticated}
      <!-- Login Form -->
      <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-center mb-6">Master Dashboard Login</h1>

        <form onsubmit={(e) => { e.preventDefault(); login(); }} class="space-y-4">
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
            <input
              id="password"
              type="password"
              bind:value={password}
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter master password"
            />
          </div>

          {#if loginError}
            <div class="text-red-600 text-sm">{loginError}</div>
          {/if}

          <button
            type="submit"
            class="w-full bg-blue-900 text-white py-2 px-4 rounded-md hover:bg-blue-800 transition-colors font-medium"
          >
            Login
          </button>
        </form>
      </div>
    {:else}
      <!-- Main Dashboard -->
      <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
          <h1 class="text-3xl font-bold text-gray-900">Master Dashboard</h1>
          <button
            onclick={logout}
            class="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
          >
            Logout
          </button>
        </div>

        {#if loading}
          <div class="text-center py-8">
            <div class="text-lg">Loading...</div>
          </div>
        {:else if dataError}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="text-red-800">{dataError}</div>
            <button
              onclick={loadData}
              class="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        {:else}
          <!-- Setup Message -->
          {#if showSetupMessage}
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div class="text-blue-800">
                Welcome! Start by creating your first user account.
              </div>
            </div>
          {/if}

          <!-- Tabs -->
          <div class="w-full">
            <div class="grid w-full grid-cols-6 bg-gray-100 rounded-lg p-1 mb-6">
              <button
                onclick={() => switchTab('users')}
                class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'users' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              >
                Users ({users?.length || 0})
              </button>
              <button
                onclick={() => switchTab('items')}
                class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'items' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              >
                Items ({items?.length || 0})
              </button>
              <button
                onclick={() => switchTab('terms')}
                class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'terms' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              >
                Terms ({terms?.length || 0})
              </button>
              <button
                onclick={() => switchTab('options')}
                class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'options' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              >
                Options ({options?.length || 0})
              </button>
              <button
                onclick={() => switchTab('item-metas')}
                class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'item-metas' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              >
                Item Metas ({itemMetas?.length || 0})
              </button>
              <button
                onclick={() => switchTab('term-metas')}
                class="px-3 py-2 text-sm font-medium rounded-md transition-colors {activeTab === 'term-metas' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'}"
              >
                Term Metas ({termMetas?.length || 0})
              </button>
            </div>

            <!-- Tab Content -->
            {#if activeTab === 'users'}
              <UsersTab
                {users}
                {token}
                onreload={loadData}
              />
            {:else if activeTab === 'items'}
              <ItemsTab
                {items}
                {users}
                {token}
                onreload={loadData}
              />
            {:else if activeTab === 'terms'}
              <TermsTab
                {terms}
                {token}
                onreload={loadData}
              />
            {:else if activeTab === 'options'}
              <OptionsTab
                {options}
                {token}
                onreload={loadData}
              />
            {:else if activeTab === 'item-metas'}
              <ItemMetasTab
                {itemMetas}
                {items}
                {token}
                onreload={loadData}
              />
            {:else if activeTab === 'term-metas'}
              <TermMetasTab
                {termMetas}
                {terms}
                {token}
                onreload={loadData}
              />
            {/if}
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>