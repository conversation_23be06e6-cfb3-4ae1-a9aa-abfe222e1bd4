import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import sql from '$lib/db/db.js';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { itemId, action } = await request.json();
		
		// Validate input
		if (!itemId || !action) {
			return json({ error: 'Missing required fields' }, { status: 400 });
		}
		
		if (!['approve', 'reject'].includes(action)) {
			return json({ error: 'Invalid action' }, { status: 400 });
		}
		
		// Update item status
		const newStatus = action === 'approve' ? 'active' : 'rejected';

		const result = await sql`
			UPDATE items
			SET item_status = ${newStatus}
			WHERE item_id = ${itemId} AND item_status = 'pending'
		`;
		
		if (result.count === 0) {
			return json({ error: 'Item not found or already processed' }, { status: 404 });
		}
		
		return json({ 
			success: true, 
			message: `Item ${action}d successfully`,
			itemId: itemId,
			newStatus: newStatus
		});
		
	} catch (error) {
		console.error('Error managing item:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
