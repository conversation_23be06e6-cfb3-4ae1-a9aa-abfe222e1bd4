import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { saveTerm } from '$lib/services/terms.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { SaveTermRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: SaveTermRequest = await request.json();
    const term = await saveTerm(body.term);

    return json(createSuccessResponse(term));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
