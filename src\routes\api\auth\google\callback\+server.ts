import { json, redirect } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { OAuth2Client } from 'google-auth-library';
import { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, PUBLIC_SITE_URL } from '$lib/utils/env.js';
import { getUserByEmail, saveUser } from '$lib/services/users.js';
import { generateUserToken } from '$lib/services/auth.js';

const oauth2Client = new OAuth2Client(
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  `${PUBLIC_SITE_URL}/api/auth/google/callback`
);

interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
}

export const GET: RequestHandler = async ({ url, cookies }) => {
  try {
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state') || '/';
    
    if (!code) {
      throw redirect(302, `/login?error=missing_code&redirectTo=${encodeURIComponent(state)}`);
    }

    // Exchange code for access token using OAuth2Client
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    // Get user info from Google using the OAuth2Client
    const ticket = await oauth2Client.verifyIdToken({
      idToken: tokens.id_token!,
      audience: GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    if (!payload) {
      throw redirect(302, `/login?error=invalid_token&redirectTo=${encodeURIComponent(state)}`);
    }

    const googleUser: GoogleUserInfo = {
      id: payload.sub,
      email: payload.email!,
      verified_email: payload.email_verified!,
      name: payload.name!,
      given_name: payload.given_name!,
      family_name: payload.family_name!,
      picture: payload.picture!,
    };

    if (!googleUser.verified_email) {
      throw redirect(302, `/login?error=email_not_verified&redirectTo=${encodeURIComponent(state)}`);
    }

    // Check if user exists or create new user
    let user = await getUserByEmail(googleUser.email);
    
    if (!user) {
      // Create new user
      user = await saveUser({
        user_firstname: googleUser.given_name || googleUser.name,
        user_email: googleUser.email.toLowerCase(),
        user_created_at: Date.now(),
        user_type: 1 // Default to member
      });
    }

    // Check if user is active
    if (user.user_type === 0) {
      throw redirect(302, `/login?error=account_inactive&redirectTo=${encodeURIComponent(state)}`);
    }

    // Generate JWT token
    const tokenPayload = {
      user_id: user.user_id!,
      user_email: user.user_email,
      user_firstname: user.user_firstname,
      user_type: user.user_type,
      type: 'user' as const
    };

    const token = generateUserToken(tokenPayload);

    // Set auth cookie
    cookies.set('auth_token', token, {
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    });

    // Redirect to original destination
    throw redirect(302, state);
    
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    
    console.error('Google OAuth callback error:', error);
    throw redirect(302, '/login?error=authentication_failed');
  }
};
