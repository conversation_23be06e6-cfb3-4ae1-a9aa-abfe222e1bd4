<script lang="ts">
  import { onMount } from 'svelte';
  import Head from '$lib/components/Head.svelte';
  import { PUBLIC_SITE_URL } from '$lib/utils/env';
  import type { GetSqliteFilesResponse, AnalyzeDatabaseResponse, RemoveAdminToolsResponse } from '$lib/types/responses.js';

  let sqliteFiles: string[] = [];
  let selectedFile: string = '';
  let isAnalyzing = false;
  let isRemoving = false;
  let message = '';
  let messageType: 'success' | 'error' | 'info' = 'info';
  let analysisResult: any = null;
  let removalStats: any = null;

  onMount(async () => {
    await loadSqliteFiles();
  });

  async function loadSqliteFiles() {
    try {
      const response = await fetch('/api/GetSqliteFiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
      
      const result: GetSqliteFilesResponse = await response.json();
      if (result.success && result.data) {
        sqliteFiles = result.data.files;
      }
    } catch (error) {
      console.error('Error loading SQLite files:', error);
      showMessage('Failed to load SQLite files', 'error');
    }
  }

  async function analyzeDatabase() {
    if (!selectedFile) {
      showMessage('Please select a SQLite file', 'error');
      return;
    }

    isAnalyzing = true;
    analysisResult = null;

    try {
      console.log('Frontend: Starting analysis for:', selectedFile);
      
      console.log('Frontend: Making API request...');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch('/api/AnalyzeDatabase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sqlite_file: selectedFile
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      console.log('Frontend: Got response');
      
      console.log('Response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result: AnalyzeDatabaseResponse = await response.json();
      console.log('Analysis result:', result);
      
      if (result.success && result.data) {
        analysisResult = result.data;
        showMessage('Database analysis completed', 'success');
      } else {
        showMessage(result.error || 'Analysis failed', 'error');
      }
    } catch (error) {
      console.error('Analysis error:', error);
      showMessage('Analysis failed: ' + (error instanceof Error ? error.message : 'Unknown error'), 'error');
    } finally {
      isAnalyzing = false;
    }
  }

  async function removeAdminTools() {
    if (!selectedFile) {
      showMessage('Please select a SQLite file', 'error');
      return;
    }

    if (!confirm(`Are you sure you want to remove all admin tools from ${selectedFile}? This action cannot be undone.`)) {
      return;
    }

    isRemoving = true;
    removalStats = null;
    
    try {
      console.log('Removing admin tools from:', selectedFile);
      
      const response = await fetch('/api/RemoveAdminTools', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sqlite_file: selectedFile
        })
      });
      
      console.log('Response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result: RemoveAdminToolsResponse = await response.json();
      console.log('Removal result:', result);
      
      if (result.success && result.data) {
        removalStats = result.data.stats;
        showMessage(result.data.message, 'success');
        // Re-analyze the database to show updated stats
        await analyzeDatabase();
      } else {
        showMessage(result.error || 'Removal failed', 'error');
      }
    } catch (error) {
      console.error('Removal error:', error);
      showMessage('Removal failed: ' + (error instanceof Error ? error.message : 'Unknown error'), 'error');
    } finally {
      isRemoving = false;
    }
  }

  function showMessage(msg: string, type: 'success' | 'error' | 'info') {
    message = msg;
    messageType = type;
    setTimeout(() => {
      message = '';
    }, 5000);
  }
</script>

<Head
  title="Prepare Databases"
  description="Prepare SQLite databases for AI Tools Directory"
  url="{PUBLIC_SITE_URL}/preparedbs"
/>

<div class="container mx-auto px-4 py-8 max-w-6xl">
  <h1 class="text-3xl font-bold mb-8 text-center">Prepare SQLite Databases</h1>
  
  <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
    <h2 class="text-lg font-semibold text-blue-800 mb-2">What this tool does:</h2>
    <p class="text-blue-700">
      This tool removes all tools created by admin users (user_role = 1 in SQLite) from SQLite databases.
      This helps clean up the data before migration by removing admin-created content and keeping only member-created tools.
    </p>
  </div>
  
  {#if message}
    <div class="mb-6 p-4 rounded-lg {messageType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : messageType === 'error' ? 'bg-red-100 text-red-800 border border-red-200' : 'bg-blue-100 text-blue-800 border border-blue-200'}">
      {message}
    </div>
  {/if}

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Database Selection and Analysis -->
    <div class="bg-white p-6 rounded-lg shadow-md border">
      <h2 class="text-xl font-semibold mb-4">Database Analysis</h2>
      
      <div class="space-y-4">
        <div>
          <label for="file-select" class="block text-sm font-medium text-gray-700 mb-2">
            Select SQLite Database File
          </label>
          <select 
            id="file-select"
            bind:value={selectedFile}
            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isAnalyzing || isRemoving}
          >
            <option value="">Choose a database file...</option>
            {#each sqliteFiles as file}
              <option value={file}>{file}</option>
            {/each}
          </select>
        </div>

        <button
          onclick={analyzeDatabase}
          disabled={isAnalyzing || !selectedFile}
          class="w-full px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {#if isAnalyzing}
            <span class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Analyzing...
            </span>
          {:else}
            Analyze Database
          {/if}
        </button>
      </div>
    </div>

    <!-- Remove Admin Tools -->
    <div class="bg-white p-6 rounded-lg shadow-md border">
      <h2 class="text-xl font-semibold mb-4 text-red-600">Remove Admin Tools</h2>
      
      <p class="text-gray-600 mb-4">
        This will permanently remove all tools created by admin users (user_role = 1) from the selected database.
        <strong class="text-red-600">This action cannot be undone!</strong>
      </p>
      
      <button
        onclick={removeAdminTools}
        disabled={isRemoving || !selectedFile || !analysisResult}
        class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        {#if isRemoving}
          <span class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Removing...
          </span>
        {:else}
          Remove Admin Tools
        {/if}
      </button>
    </div>
  </div>

  <!-- Analysis Results -->
  {#if analysisResult}
    <div class="mt-8 bg-white p-6 rounded-lg shadow-md border">
      <h2 class="text-xl font-semibold mb-4">Database Analysis Results</h2>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
          <div class="text-2xl font-bold text-blue-600">{analysisResult.total_users}</div>
          <div class="text-sm text-gray-600">Total Users</div>
        </div>
        
        <div class="text-center p-4 bg-red-50 rounded-lg">
          <div class="text-2xl font-bold text-red-600">{analysisResult.admin_users}</div>
          <div class="text-sm text-gray-600">Admin Users</div>
        </div>
        
        <div class="text-center p-4 bg-green-50 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{analysisResult.member_users}</div>
          <div class="text-sm text-gray-600">Member Users</div>
        </div>
        
        <div class="text-center p-4 bg-purple-50 rounded-lg">
          <div class="text-2xl font-bold text-purple-600">{analysisResult.total_tools}</div>
          <div class="text-sm text-gray-600">Total Tools</div>
        </div>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div class="text-center p-4 bg-red-100 rounded-lg border-2 border-red-200">
          <div class="text-2xl font-bold text-red-700">{analysisResult.admin_tools}</div>
          <div class="text-sm text-gray-600">Admin Tools</div>
          <div class="text-xs text-red-600 mt-1">Will be removed</div>
        </div>
        
        <div class="text-center p-4 bg-green-100 rounded-lg border-2 border-green-200">
          <div class="text-2xl font-bold text-green-700">{analysisResult.member_tools}</div>
          <div class="text-sm text-gray-600">Member Tools</div>
          <div class="text-xs text-green-600 mt-1">Will be kept</div>
        </div>
        
        <div class="text-center p-4 bg-yellow-50 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600">{analysisResult.admin_relationships}</div>
          <div class="text-sm text-gray-600">Admin Relationships</div>
          <div class="text-xs text-yellow-600 mt-1">Will be removed</div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Removal Statistics -->
  {#if removalStats}
    <div class="mt-8 bg-white p-6 rounded-lg shadow-md border">
      <h2 class="text-xl font-semibold mb-4">Removal Statistics</h2>
      
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center p-4 bg-red-50 rounded-lg">
          <div class="text-2xl font-bold text-red-600">{removalStats.tools_removed}</div>
          <div class="text-sm text-gray-600">Tools Removed</div>
        </div>
        
        <div class="text-center p-4 bg-orange-50 rounded-lg">
          <div class="text-2xl font-bold text-orange-600">{removalStats.relationships_removed}</div>
          <div class="text-sm text-gray-600">Relationships Removed</div>
        </div>
      </div>
    </div>
  {/if}
</div>
