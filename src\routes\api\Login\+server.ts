import { json } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { validateMasterPassword, generateMasterToken } from '$lib/services/auth.js';
import type { LoginRequest } from '$lib/types/requests.js';
import type { LoginResponse } from '$lib/types/responses.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: LoginRequest = await request.json();
    
    if (!validateMasterPassword(body.password)) {
      const response: LoginResponse = {
        success: false,
        error: 'Invalid password'
      };
      return json(response, { status: 401 });
    }
    
    const token = generateMasterToken();
    const response: LoginResponse = {
      success: true,
      data: { token }
    };
    
    return json(response);
  } catch (error) {
    const response: LoginResponse = {
      success: false,
      error: 'Invalid request'
    };
    return json(response, { status: 400 });
  }
};
