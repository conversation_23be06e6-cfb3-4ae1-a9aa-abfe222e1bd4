<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { Option } from '$lib/types/tables.js';

  interface Props {
    options?: Option[];
    token?: string;
    onreload?: () => void;
  }

  let { options = [], token = '', onreload = () => {} }: Props = $props();

  // API helper function
  async function apiCall(endpoint: string, data?: any) {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }



  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = $state(1);
  let searchQuery = $state('');

  // Form states
  let showAddDialog = $state(false);
  let showEditDialog = $state(false);

  // Form data
  let newOption = $state<Partial<Option>>({
    option_key: '',
    option_value: ''
  });
  let editOption = $state<Partial<Option>>({});

  // Filtering and pagination functions
  function filterOptions() {
    if (!options || !Array.isArray(options)) return [];
    // Only filter if search query is more than 2 characters
    if (!searchQuery || searchQuery.length <= 2) return options;
    const query = searchQuery.toLowerCase();
    return options.filter(option =>
      option.option_key?.toLowerCase().includes(query) ||
      option.option_value?.toLowerCase().includes(query)
    );
  }

  function paginateData(data: Option[]) {
    if (!data || !Array.isArray(data)) return [];
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  }

  function getTotalPages(dataLength: number) {
    return Math.ceil(dataLength / itemsPerPage);
  }

  function changePage(page: number) {
    currentPage = page;
  }

  // Reload data function - this will trigger parent to reload
  function reloadData() {
    onreload();
  }

  function openEditOption(option: Option) {
    editOption = { ...option };
    showEditDialog = true;
  }

  async function addOption() {
    try {
      await apiCall('/api/SaveOption', { option: newOption });
      newOption = {
        option_key: '',
        option_value: ''
      };
      showAddDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to add option:', error);
      alert('Failed to add option');
    }
  }

  async function updateOption() {
    try {
      await apiCall('/api/SaveOption', { option: editOption });
      showEditDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to update option:', error);
      alert('Failed to update option');
    }
  }

  async function deleteOption(id: number) {
    if (confirm('Are you sure you want to delete this option?')) {
      try {
        await apiCall('/api/DeleteOption', { option_id: id });
        reloadData();
      } catch (error) {
        console.error('Failed to delete option:', error);
        alert('Failed to delete option');
      }
    }
  }

  // Reactive statements for filtered and paginated data
  let filteredOptions = $derived(filterOptions());
  let paginatedOptions = $derived(paginateData(filteredOptions));
  let totalPages = $derived(getTotalPages(filteredOptions.length));

  // Reset to page 1 when search query changes
  $effect(() => {
    if (searchQuery) {
      currentPage = 1;
    }
  });
</script>

<div class="space-y-4">
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold">Options Management</h2>
    <button
      onclick={() => showAddDialog = true}
      class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium"
    >
      Add New Option
    </button>
  </div>

  <!-- Search Box -->
  <div class="flex gap-4 items-center">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search options by key or value (min 3 characters)..."
        bind:value={searchQuery}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="text-sm text-gray-600">
      {searchQuery && searchQuery.length > 2 ? `${filteredOptions?.length || 0} of ${options?.length || 0}` : options?.length || 0} options
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {#each paginatedOptions as option}
      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
        <div class="p-6">
          <div class="space-y-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-lg text-gray-900 mb-1">{option.option_key}</h3>
                <p class="text-sm text-gray-600 break-all">{option.option_value || 'No value'}</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ID: {option.option_id}
              </span>
            </div>

            <div class="flex gap-2 pt-2">
              <button 
                onclick={() => openEditOption(option)}
                class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Edit
              </button>
              <button
                onclick={() => deleteOption(option.option_id!)}
                class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="flex justify-center items-center gap-4 mt-6">
      <button
        onclick={() => changePage(currentPage - 1)}
        disabled={currentPage === 1}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Previous
      </button>

      <span class="text-gray-600">
        Page {currentPage} of {totalPages}
      </span>

      <button
        onclick={() => changePage(currentPage + 1)}
        disabled={currentPage === totalPages}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Next
      </button>
    </div>
  {/if}
</div>

<!-- Add Option Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Add New Option</Dialog.Title>
        <Dialog.Description>Create a new option.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="option_key" class="text-right text-sm font-medium">Key</label>
          <input
            id="option_key"
            type="text"
            bind:value={newOption.option_key}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="option_key"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="option_value" class="text-right text-sm font-medium">Value</label>
          <input
            id="option_value"
            type="text"
            bind:value={newOption.option_value}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Option value"
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showAddDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={addOption}
          disabled={!newOption.option_key || !newOption.option_value}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Add Option
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit Option Dialog -->
<Dialog.Root bind:open={showEditDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Edit Option</Dialog.Title>
        <Dialog.Description>Update option information.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_option_key" class="text-right text-sm font-medium">Key</label>
          <input
            id="edit_option_key"
            type="text"
            bind:value={editOption.option_key}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="option_key"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_option_value" class="text-right text-sm font-medium">Value</label>
          <input
            id="edit_option_value"
            type="text"
            bind:value={editOption.option_value}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Option value"
          />
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showEditDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={updateOption}
          disabled={!editOption.option_key || !editOption.option_value}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Update Option
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
