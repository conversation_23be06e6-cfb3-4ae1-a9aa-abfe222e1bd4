import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { deleteTerm } from '$lib/services/terms.js';
import { authenticateRequest, createUnauthorizedResponse, createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { DeleteTermRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    if (!authenticateRequest(request)) {
      return json(createUnauthorizedResponse(), { status: 401 });
    }
    
    const body: DeleteTermRequest = await request.json();
    const deleted = await deleteTerm(body.term_id);
    
    return json(createSuccessResponse({ deleted }));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
