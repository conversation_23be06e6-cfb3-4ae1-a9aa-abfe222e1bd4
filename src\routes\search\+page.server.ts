import type { PageServerLoad } from './$types';
import { searchItems } from '$lib/services/items';

export const load: PageServerLoad = async ({ url }) => {
	try {
		// Get search query from URL params
		const query = url.searchParams.get('q') || '';

		// Get page from URL params
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = 40;
		const offset = (page - 1) * limit;

		// If no query, return empty results
		if (!query || query.trim() === '') {
			return {
				items: [],
				total: 0,
				query: '',
				currentPage: page,
				totalPages: 0,
				hasNextPage: false,
				hasPrevPage: false
			};
		}

		// Search items with metadata
		const { items, total } = await searchItems(query, limit, offset);

		// Calculate pagination info
		const totalPages = Math.ceil(total / limit);
		const hasNextPage = page < totalPages;
		const hasPrevPage = page > 1;

		return {
			items,
			total,
			query: query.trim(),
			currentPage: page,
			totalPages,
			hasNextPage,
			hasPrevPage
		};
	} catch (error) {
		console.error('Error loading search page:', error);
		return {
			items: [],
			total: 0,
			query: '',
			currentPage: 1,
			totalPages: 0,
			hasNextPage: false,
			hasPrevPage: false
		};
	}
};
