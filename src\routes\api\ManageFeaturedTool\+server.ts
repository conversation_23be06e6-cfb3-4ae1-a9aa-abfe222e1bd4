import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import sql from '$lib/db/db.js';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { itemId, action } = await request.json();
		
		// Validate input
		if (!itemId || !action) {
			return json({ error: 'Missing required fields' }, { status: 400 });
		}
		
		if (!['add', 'remove'].includes(action)) {
			return json({ error: 'Invalid action' }, { status: 400 });
		}
		
		// Update item status
		let newStatus: number;
		let message: string;
		
		if (action === 'add') {
			// Set to featured (status 2)
			newStatus = 2;
			message = 'Tool added to featured';
		} else {
			// Set to active (status 1)
			newStatus = 1;
			message = 'Tool removed from featured';
		}

		const result = await sql`
			UPDATE items
			SET item_status = ${newStatus}
			WHERE item_id = ${itemId}
		`;
		
		if (result.count === 0) {
			return json({ error: 'Item not found' }, { status: 404 });
		}
		
		return json({ 
			success: true, 
			message,
			itemId,
			newStatus
		});
		
	} catch (error) {
		console.error('Error managing featured tool:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
