<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import type { User } from '$lib/types/tables.js';

  export let users: User[] = [];
  export let token: string = '';
  export let onreload: () => void = () => {};

  // API helper function
  async function apiCall(endpoint: string, data?: any) {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  // Pagination and search states
  let itemsPerPage = 40;
  let currentPage = 1;
  let searchQuery = '';
  let totalUsers = 0;
  let displayedUsers: User[] = [];
  let loading = false;

  // Form states
  let showAddDialog = false;
  let showEditDialog = false;

  // Form data
  let newUser: Partial<User> = {
    user_firstname: '',
    user_email: '',
    user_type: 1
  };
  let editUser: Partial<User> = {};

  // Server-side pagination functions
  async function loadUsers() {
    try {
      loading = true;
      const offset = (currentPage - 1) * itemsPerPage;

      console.log(`Loading users: page ${currentPage}, limit ${itemsPerPage}, offset ${offset}`);

      const response = await apiCall('/api/GetUsers', {
        limit: itemsPerPage,
        offset: offset
      });

      console.log('Users API response:', response);

      if (response.success && response.data) {
        displayedUsers = response.data.users || [];
        totalUsers = response.data.total || 0;
        console.log(`Loaded ${displayedUsers.length} users, total: ${totalUsers}`);
      } else {
        displayedUsers = [];
        totalUsers = 0;
        console.error('Failed to load users:', response);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      displayedUsers = [];
      totalUsers = 0;
    } finally {
      loading = false;
    }
  }

  function getTotalPages() {
    return Math.ceil(totalUsers / itemsPerPage);
  }

  async function changePage(page: number) {
    if (page >= 1 && page <= getTotalPages()) {
      currentPage = page;
      await loadUsers();
    }
  }

  // Reload data function
  async function reloadData() {
    await loadUsers();
    onreload(); // Also trigger parent reload for tab counts
  }

  function openEditUser(user: User) {
    editUser = { ...user };
    showEditDialog = true;
  }

  async function addUser() {
    try {
      await apiCall('/api/SaveUser', { user: newUser });
      newUser = {
        user_firstname: '',
        user_email: '',
        user_type: 1
      };
      showAddDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to add user:', error);
      alert('Failed to add user');
    }
  }

  async function updateUser() {
    try {
      await apiCall('/api/SaveUser', { user: editUser });
      showEditDialog = false;
      reloadData();
    } catch (error) {
      console.error('Failed to update user:', error);
      alert('Failed to update user');
    }
  }

  async function deleteUser(id: number) {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        await apiCall('/api/DeleteUser', { user_id: id });
        reloadData();
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert('Failed to delete user');
      }
    }
  }

  // Reactive statements
  $: totalPages = getTotalPages();

  // Load users when component mounts
  $: if (token && users && users.length > 0 && currentPage === 1 && displayedUsers.length === 0) {
    // Initial load from server
    loadUsers();
  }
</script>

<div class="space-y-4">
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold">Users Management</h2>
    <button
      onclick={() => showAddDialog = true}
      class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 transition-colors text-sm font-medium"
    >
      Add New User
    </button>
  </div>

  <!-- Search Box -->
  <div class="flex gap-4 items-center">
    <div class="flex-1">
      <input
        type="text"
        placeholder="Search users by name or email (min 3 characters)..."
        bind:value={searchQuery}
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>
    <div class="text-sm text-gray-600">
      {loading ? 'Loading...' : `${displayedUsers?.length || 0} of ${totalUsers || 0} users`}
    </div>
  </div>

  {#if loading}
    <div class="flex justify-center items-center py-8">
      <div class="text-gray-500">Loading users...</div>
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {#each displayedUsers as user}
      <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200">
        <div class="p-6">
          <div class="space-y-3">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-semibold text-lg text-gray-900 mb-1">{user.user_firstname}</h3>
                <p class="text-sm text-gray-600 break-all">{user.user_email}</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                ID: {user.user_id}
              </span>
            </div>

            <div class="space-y-1">
              <div class="flex items-center gap-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {user.user_type === 0 ? 'Inactive' : user.user_type === 1 ? 'Member' : 'Admin'}
                </span>
              </div>
              <p class="text-xs text-gray-500">
                Created: {new Date(Number(user.user_created_at)).toLocaleDateString()}
              </p>
            </div>

            <div class="flex gap-2 pt-2">
              <button 
                onclick={() => openEditUser(user)}
                class="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-sm hover:bg-gray-50 transition-colors"
              >
                Edit
              </button>
              <button
                onclick={() => deleteUser(user.user_id!)}
                class="flex-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="flex justify-center items-center gap-4 mt-6">
      <button
        onclick={() => changePage(currentPage - 1)}
        disabled={currentPage === 1}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Previous
      </button>
      
      <span class="text-gray-600">
        Page {currentPage} of {totalPages}
      </span>
      
      <button
        onclick={() => changePage(currentPage + 1)}
        disabled={currentPage === totalPages}
        class="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
      >
        Next
      </button>
    </div>
  {/if}
{/if}
</div>

<!-- Add User Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Add New User</Dialog.Title>
        <Dialog.Description>Create a new user account.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="user_firstname" class="text-right text-sm font-medium">Name</label>
          <input
            id="user_firstname"
            type="text"
            bind:value={newUser.user_firstname}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="First name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="user_email" class="text-right text-sm font-medium">Email</label>
          <input
            id="user_email"
            type="email"
            bind:value={newUser.user_email}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="user_type" class="text-right text-sm font-medium">Type</label>
          <select
            id="user_type"
            bind:value={newUser.user_type}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>Inactive</option>
            <option value={1}>Member</option>
            <option value={2}>Admin</option>
          </select>
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showAddDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={addUser}
          disabled={!newUser.user_firstname || !newUser.user_email}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Add User
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Edit User Dialog -->
<Dialog.Root bind:open={showEditDialog}>
  <Dialog.Portal>
    <Dialog.Overlay />
    <Dialog.Content class="max-w-md">
      <Dialog.Header>
        <Dialog.Title>Edit User</Dialog.Title>
        <Dialog.Description>Update user information.</Dialog.Description>
      </Dialog.Header>
      <div class="p-6 space-y-4">
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_user_firstname" class="text-right text-sm font-medium">Name</label>
          <input
            id="edit_user_firstname"
            type="text"
            bind:value={editUser.user_firstname}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="First name"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_user_email" class="text-right text-sm font-medium">Email</label>
          <input
            id="edit_user_email"
            type="email"
            bind:value={editUser.user_email}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
        <div class="grid grid-cols-4 items-center gap-4">
          <label for="edit_user_type" class="text-right text-sm font-medium">Type</label>
          <select
            id="edit_user_type"
            bind:value={editUser.user_type}
            class="col-span-3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>Inactive</option>
            <option value={1}>Member</option>
            <option value={2}>Admin</option>
          </select>
        </div>
      </div>
      <Dialog.Footer>
        <button
          onclick={() => showEditDialog = false}
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm font-medium"
        >
          Cancel
        </button>
        <button
          onclick={updateUser}
          disabled={!editUser.user_firstname || !editUser.user_email}
          class="px-4 py-2 bg-blue-900 text-white rounded-md hover:bg-blue-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Update User
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
