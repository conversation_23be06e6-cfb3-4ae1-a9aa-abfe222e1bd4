import { getActiveItemsByTermWithMetadata } from '$lib/services/items.js';
import { getTerms } from '$lib/services/terms.js';
import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, url }) => {
	try {
		const slug = params.slug;

		// Get page from URL params
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = 40;
		const offset = (page - 1) * limit;

		// Get the pricing term first to verify it exists
		const terms = await getTerms('pricing');
		const pricing = terms.find(t => t.term_slug === slug);
		
		if (!pricing) {
			throw error(404, 'Pricing category not found');
		}
		
		// Get items for this pricing category (only active or featured, with metadata)
		const { items, total } = await getActiveItemsByTermWithMetadata(slug, 'pricing', limit, offset);

		// Calculate pagination info
		const totalPages = Math.ceil(total / limit);
		const hasNextPage = page < totalPages;
		const hasPrevPage = page > 1;

		return {
			pricing,
			items,
			total,
			currentPage: page,
			totalPages,
			hasNextPage,
			hasPrevPage
		};
	} catch (err) {
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		console.error('Error loading pricing data:', err);
		throw error(500, 'Failed to load pricing data');
	}
};
