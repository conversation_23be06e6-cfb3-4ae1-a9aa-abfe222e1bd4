import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getOptions } from '$lib/services/options.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    const limit = body.limit || 50;
    const offset = body.offset || 0;

    const result = await getOptions(limit, offset);

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
