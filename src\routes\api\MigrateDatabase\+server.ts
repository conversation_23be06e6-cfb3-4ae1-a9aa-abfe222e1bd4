import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { migrateDatabase } from '$lib/services/migration.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { MigrateDatabaseRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    console.log('MigrateDatabase API called');
    const body: MigrateDatabaseRequest = await request.json();
    console.log('Request body:', body);

    if (!body.sqlite_file) {
      console.log('Missing required fields:', { sqlite_file: body.sqlite_file });
      return json(createErrorResponse('sqlite_file is required'), { status: 400 });
    }

    console.log('Starting migration with:', { sqlite_file: body.sqlite_file });
    const result = await migrateDatabase(body.sqlite_file);
    console.log('Migration completed successfully');

    return json(createSuccessResponse(result));
  } catch (error) {
    console.error('Migration API error:', error);
    return json(createErrorResponse(error), { status: 500 });
  }
};
