import sql from '$lib/db/db.js';
import { readdir } from 'fs/promises';
import { join } from 'path';
import Database from 'better-sqlite3';

export async function getSqliteFiles(): Promise<string[]> {
  try {
    const dbsPath = join(process.cwd(), 'dbs');
    const files = await readdir(dbsPath);
    return files.filter(file => file.endsWith('.db') && !file.includes('-shm') && !file.includes('-wal'));
  } catch (error) {
    console.error('Error reading dbs folder:', error);
    return [];
  }
}

export async function cleanDatabase(): Promise<{ cleaned: boolean; message: string }> {
  try {
    await sql.begin(async (sql) => {
      await sql`DELETE FROM item_term_relationships`;
      await sql`DELETE FROM term_metas`;
      await sql`DELETE FROM item_metas`;
      await sql`DELETE FROM terms`;
      await sql`DELETE FROM items`;
      await sql`DELETE FROM users`;
      await sql`DELETE FROM options WHERE option_key != 'migration_status'`;
    });

    return { cleaned: true, message: 'Database cleaned successfully.' };
  } catch (error) {
    console.error('Error cleaning database:', error);
    throw new Error(`Failed to clean database: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

interface MigrationStats {
  users_migrated: number;
  items_migrated: number;
  terms_migrated: number;
  item_metas_migrated: number;
  term_metas_migrated: number;
  relationships_migrated: number;
}

export async function migrateDatabase(sqlite_file: string): Promise<{ success: boolean; message: string; stats: MigrationStats }> {
  const stats: MigrationStats = {
    users_migrated: 0,
    items_migrated: 0,
    terms_migrated: 0,
    item_metas_migrated: 0,
    term_metas_migrated: 0,
    relationships_migrated: 0
  };

  try {
    const dbPath = join(process.cwd(), 'dbs', sqlite_file);
    console.log('Opening SQLite database at:', dbPath);

    // Check if file exists
    const fs = await import('fs');
    if (!fs.existsSync(dbPath)) {
      throw new Error(`SQLite file not found: ${dbPath}`);
    }
    console.log('SQLite file exists');

    const sqliteDb = new Database(dbPath, { readonly: true });
    console.log('SQLite database opened successfully');

    const userIdMapping = new Map<number, number>();
    const itemIdMapping = new Map<number, number>();
    const termIdMapping = new Map<number, number>();

    console.log('Querying SQLite users table');
    const oldUsers = sqliteDb.prepare('SELECT * FROM users').all() as any[];
    console.log(`Found ${oldUsers.length} users to migrate`);

    console.log('Starting user migration (without large transaction)');

    for (let i = 0; i < oldUsers.length; i++) {
      const oldUser = oldUsers[i];
      console.log(`Migrating user ${i + 1}/${oldUsers.length}: ${oldUser.user_email}`);

      // Map old user_role to new user_type: 0=inactive, 1=member, 2=admin
      const userType = oldUser.user_role === 1 ? 2 : 1; // old role 1 (admin) -> new type 2 (admin), others -> type 1 (member)

      try {
        const [newUser] = await sql`
          INSERT INTO users (user_firstname, user_email, user_created_at, user_type)
          VALUES (${oldUser.user_firstname}, ${oldUser.user_email}, ${oldUser.user_created_at}, ${userType})
          RETURNING user_id
        `;

        userIdMapping.set(oldUser.user_id, newUser.user_id);
        stats.users_migrated++;

        if ((i + 1) % 10 === 0) {
          console.log(`Migrated ${i + 1} users so far...`);
        }
      } catch (userError: any) {
        console.error(`Error migrating user ${oldUser.user_email}:`, userError);

        // Check if it's a unique constraint violation (duplicate email)
        if (userError?.code === '23505') {
          console.log(`Skipping duplicate user: ${oldUser.user_email}`);
          // Still need to map the user ID for relationships
          // Try to find the existing user
          try {
            const [existingUser] = await sql`
              SELECT user_id FROM users WHERE user_email = ${oldUser.user_email}
            `;
            if (existingUser) {
              userIdMapping.set(oldUser.user_id, existingUser.user_id);
              console.log(`Mapped to existing user ID: ${existingUser.user_id}`);
            }
          } catch (findError) {
            console.error(`Error finding existing user:`, findError);
          }
        } else {
          throw userError;
        }
      }
    }

    console.log(`Completed user migration: ${stats.users_migrated} users migrated`);

    console.log('Starting items migration');
    const oldTools = sqliteDb.prepare('SELECT * FROM tools').all() as any[];
    console.log(`Found ${oldTools.length} tools to migrate`);

    for (let i = 0; i < oldTools.length; i++) {
      const oldTool = oldTools[i];
      console.log(`Migrating tool ${i + 1}/${oldTools.length}: ${oldTool.tool_name}`);

      // Set all items to active status by default (1=active)
      const itemStatus = 1;
      const newUserId = userIdMapping.get(oldTool.user_id);

      if (!newUserId) {
        console.warn(`Skipping tool ${oldTool.tool_id}: user ${oldTool.user_id} not found`);
        continue;
      }

      try {
        const [newItem] = await sql`
          INSERT INTO items (item_name, item_slug, item_url, item_status, item_created_at, user_id)
          VALUES (${oldTool.tool_name}, ${oldTool.tool_slug}, ${oldTool.tool_site_url || ''}, ${itemStatus}, ${oldTool.tool_created_at}, ${newUserId})
          RETURNING item_id
        `;

        itemIdMapping.set(oldTool.tool_id, newItem.item_id);
        stats.items_migrated++;

        if ((i + 1) % 10 === 0) {
          console.log(`Migrated ${i + 1} tools so far...`);
        }
      } catch (toolError) {
        console.error(`Error migrating tool ${oldTool.tool_name}:`, toolError);
        throw toolError;
      }
    }

    console.log(`Completed items migration: ${stats.items_migrated} items migrated`);

    console.log('Starting terms migration');
    const oldTerms = sqliteDb.prepare('SELECT * FROM terms').all() as any[];
    console.log(`Found ${oldTerms.length} terms to migrate`);

    for (let i = 0; i < oldTerms.length; i++) {
      const oldTerm = oldTerms[i];
      console.log(`Migrating term ${i + 1}/${oldTerms.length}: ${oldTerm.term_name}`);

      try {
        const [newTerm] = await sql`
          INSERT INTO terms (term_name, term_slug, term_taxonomy)
          VALUES (${oldTerm.term_name}, ${oldTerm.term_slug}, ${oldTerm.term_taxonomy})
          RETURNING term_id
        `;

        termIdMapping.set(oldTerm.term_id, newTerm.term_id);
        stats.terms_migrated++;

        if ((i + 1) % 10 === 0) {
          console.log(`Migrated ${i + 1} terms so far...`);
        }
      } catch (termError) {
        console.error(`Error migrating term ${oldTerm.term_name}:`, termError);
        throw termError;
      }
    }

    console.log(`Completed terms migration: ${stats.terms_migrated} terms migrated`);

    console.log('Starting item metas migration');
    for (let i = 0; i < oldTools.length; i++) {
      const oldTool = oldTools[i];
      const newItemId = itemIdMapping.get(oldTool.tool_id);
      if (!newItemId) continue;

      console.log(`Migrating item metas ${i + 1}/${oldTools.length} for: ${oldTool.tool_name}`);

      const metaFields = [
        { key: 'content', value: oldTool.tool_content },
        { key: 'description', value: oldTool.tool_summary }, // tool_summary becomes 'description'
        { key: 'dofollow', value: oldTool.tool_dofollow?.toString() }
      ];

      for (const meta of metaFields) {
        if (meta.value && meta.value.trim() !== '') {
          try {
            await sql`
              INSERT INTO item_metas (item_id, item_meta_key, item_meta_value)
              VALUES (${newItemId}, ${meta.key}, ${meta.value})
            `;
            stats.item_metas_migrated++;
          } catch (metaError) {
            console.error(`Error migrating item meta ${meta.key} for ${oldTool.tool_name}:`, metaError);
            throw metaError;
          }
        }
      }
    }

    console.log(`Completed item metas migration: ${stats.item_metas_migrated} item metas migrated`);

    console.log('Starting term metas migration');
    for (let i = 0; i < oldTerms.length; i++) {
      const oldTerm = oldTerms[i];
      const newTermId = termIdMapping.get(oldTerm.term_id);
      if (!newTermId) continue;

      console.log(`Migrating term metas ${i + 1}/${oldTerms.length} for: ${oldTerm.term_name}`);

      const metaFields = [
        { key: 'content', value: oldTerm.term_content },
        { key: 'description', value: oldTerm.term_summary }
      ];

      for (const meta of metaFields) {
        if (meta.value && meta.value.trim() !== '') {
          try {
            await sql`
              INSERT INTO term_metas (term_id, term_meta_key, term_meta_value)
              VALUES (${newTermId}, ${meta.key}, ${meta.value})
            `;
            stats.term_metas_migrated++;
          } catch (metaError) {
            console.error(`Error migrating term meta ${meta.key} for ${oldTerm.term_name}:`, metaError);
            throw metaError;
          }
        }
      }
    }

    console.log(`Completed term metas migration: ${stats.term_metas_migrated} term metas migrated`);

    console.log('Starting relationships migration');
    const oldRelationships = sqliteDb.prepare('SELECT * FROM tool_term_relationships').all() as any[];
    console.log(`Found ${oldRelationships.length} relationships to migrate`);

    for (let i = 0; i < oldRelationships.length; i++) {
      const oldRel = oldRelationships[i];
      const newItemId = itemIdMapping.get(oldRel.tool_id);
      const newTermId = termIdMapping.get(oldRel.term_id);

      if (newItemId && newTermId) {
        try {
          await sql`
            INSERT INTO item_term_relationships (item_id, term_id)
            VALUES (${newItemId}, ${newTermId})
          `;
          stats.relationships_migrated++;
        } catch (relError) {
          console.error(`Error migrating relationship ${oldRel.tool_id}->${oldRel.term_id}:`, relError);
          throw relError;
        }
      }

      if ((i + 1) % 10 === 0) {
        console.log(`Migrated ${i + 1} relationships so far...`);
      }
    }

    console.log(`Completed relationships migration: ${stats.relationships_migrated} relationships migrated`);

    sqliteDb.close();

    return {
      success: true,
      message: `Migration completed successfully from ${sqlite_file}`,
      stats
    };

  } catch (error) {
    console.error('Migration error:', error);
    throw new Error(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
