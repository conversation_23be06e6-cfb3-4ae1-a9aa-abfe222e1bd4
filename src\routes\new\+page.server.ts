import { getItemsWithMetadata } from '$lib/services/items.js';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url }) => {
	try {
		// Get page from URL params
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = 40;
		const offset = (page - 1) * limit;

		// Get items with metadata sorted by date (newest first, only active or featured)
		const { items, total } = await getItemsWithMetadata('active', limit, offset);

		// Calculate pagination info
		const totalPages = Math.ceil(total / limit);
		const hasNextPage = page < totalPages;
		const hasPrevPage = page > 1;

		return {
			items,
			total,
			currentPage: page,
			totalPages,
			hasNextPage,
			hasPrevPage
		};
	} catch (error) {
		console.error('Error loading new tools data:', error);
		return {
			items: [],
			total: 0,
			currentPage: 1,
			totalPages: 0,
			hasNextPage: false,
			hasPrevPage: false
		};
	}
};
