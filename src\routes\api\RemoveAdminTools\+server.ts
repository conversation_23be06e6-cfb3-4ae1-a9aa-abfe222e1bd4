import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { removeAdminTools } from '$lib/services/preparedb.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { RemoveAdminToolsRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    console.log('RemoveAdminTools API called');
    const body: RemoveAdminToolsRequest = await request.json();
    console.log('Request body:', body);
    
    if (!body.sqlite_file) {
      console.log('Missing sqlite_file parameter');
      return json(createErrorResponse('sqlite_file is required'), { status: 400 });
    }
    
    console.log('Starting admin tools removal for:', body.sqlite_file);
    const result = await removeAdminTools(body.sqlite_file);
    console.log('Admin tools removal completed:', result);
    
    return json(createSuccessResponse(result));
  } catch (error) {
    console.error('RemoveAdminTools API error:', error);
    return json(createErrorResponse(error), { status: 500 });
  }
};
