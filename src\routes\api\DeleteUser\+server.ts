import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { deleteUser } from '$lib/services/users.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { DeleteUserRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: DeleteUserRequest = await request.json();
    const deleted = await deleteUser(body.user_id);

    return json(createSuccessResponse({ deleted }));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
