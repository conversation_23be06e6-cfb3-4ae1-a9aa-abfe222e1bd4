<script lang="ts">
	import { Image } from 'lucide-svelte';

	interface ImageUploadProps {
		id: string;
		label: string;
		accept?: string;
		maxSize?: number; // in bytes
		preview?: string | null;
		isUploading?: boolean;
		required?: boolean;
		className?: string;
		onFileSelect?: (file: File) => void;
		onDrop?: (event: DragEvent) => void;
		onTriggerUpload?: () => void;
		aspectRatio?: 'video' | 'square' | 'auto';
		previewAlt?: string;
	}

	let {
		id,
		label,
		accept = 'image/*',
		maxSize = 2 * 1024 * 1024, // 2MB default
		preview = null,
		isUploading = false,
		required = false,
		className = '',
		onFileSelect,
		onDrop,
		onTriggerUpload,
		aspectRatio = 'video',
		previewAlt = 'Image preview'
	}: ImageUploadProps = $props();

	function handleDragOver(event: DragEvent) {
		event.preventDefault();
	}

	function handleFileChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const file = target.files?.[0];
		if (file && onFileSelect) {
			onFileSelect(file);
		}
	}

	function handleClick() {
		if (onTriggerUpload) {
			onTriggerUpload();
		}
	}

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter' && onTriggerUpload) {
			onTriggerUpload();
		}
	}

	const aspectRatioClass = aspectRatio === 'video' ? 'aspect-video' :
		aspectRatio === 'square' ? 'aspect-square' :
		'h-48';

	const previewClass = aspectRatio === 'square' ? 'object-contain' : 'object-cover';
</script>

<div class={className}>
	<label for={id} class="block text-sm font-medium text-gray-700 mb-1">
		{label}
		{#if required}<span class="text-red-500">*</span>{/if}
	</label>
	
	<div
		class="mt-1 relative border-2 border-gray-300 border-dashed rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 cursor-pointer {aspectRatioClass}"
		ondrop={onDrop}
		ondragover={handleDragOver}
		onclick={handleClick}
		role="button"
		tabindex="0"
		onkeydown={handleKeyDown}
	>
		{#if preview}
			<div class="relative w-full h-full">
				<img
					src={preview}
					alt={previewAlt}
					class="w-full h-full {previewClass} rounded-lg"
					loading="lazy"
					decoding="async"
				/>
				<div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
					<p class="text-white text-sm font-medium">Click to change image</p>
				</div>
				{#if isUploading}
					<div class="absolute inset-0 bg-white bg-opacity-75 rounded-lg flex items-center justify-center">
						<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-900"></div>
					</div>
				{/if}
			</div>
		{:else}
			<div class="flex justify-center items-center h-full px-6">
				<div class="text-center">
					{#if isUploading}
						<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto"></div>
						<p class="text-sm text-gray-600 mt-2">Compressing and uploading...</p>
					{:else}
						<Image class="mx-auto h-12 w-12 text-gray-400" />
						<div class="text-sm text-gray-600 mt-2">
							<span class="font-medium text-blue-900 hover:text-orange-500">Upload a file</span>
							<span class="pl-1">or drag and drop</span>
						</div>
						<p class="text-xs text-gray-500 mt-1">
							PNG, JPG, WebP up to {Math.round(maxSize / (1024 * 1024))}MB
						</p>
						<p class="text-xs text-gray-400 mt-1">
							Images will be compressed to 50% quality for optimal performance
						</p>
					{/if}
				</div>
			</div>
		{/if}
		
		<input
			{id}
			type="file"
			class="sr-only"
			{accept}
			onchange={handleFileChange}
		/>
	</div>
</div>
