import { json } from '@sveltejs/kit';
import type { Request<PERSON>and<PERSON> } from './$types';
import { getTermMetas } from '$lib/services/term_metas.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    const term_id = body.term_id;
    const limit = body.limit || 50;
    const offset = body.offset || 0;

    const result = await getTermMetas(term_id, limit, offset);

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
