# AI Tools Directory - Metadata Documentation

This document outlines the metadata structure and usage patterns for the AI Tools Directory application. This information is crucial for understanding how data is organized and displayed across different pages.

## Database Schema Overview

### Core Tables
- **items**: Main tool/product entries
- **terms**: Categories, pricing tiers, and other taxonomies
- **item_metas**: Key-value metadata for items
- **term_metas**: Key-value metadata for terms
- **item_term_relationships**: Many-to-many relationships between items and terms

## Item Metadata (item_metas)

### Core Content Fields
- **`content`**: Full detailed description/article content for the tool (used in /ai/[slug] page)
- **`description`**: Primary description used in cards, listings, and as fallback content

### Visual Assets
- **`thumbnail_url`**: Card thumbnail image (400x225 aspect ratio recommended)
- **`screenshot_url`**: Full-size screenshot displayed on tool detail page
- **`icon_url`**: Small icon/logo (used in featured tools sidebar, 48x48px recommended)

### SEO & External Links
- **`dofollow`**: Controls link attributes - when value is "1" links are dofollow, when "0" or any other value links are nofollow
- **`item_url`**: External website URL for the tool (stored in items table, not item_metas)

### Default Fallback Images
- Default screenshot: `/assets/images/default-screenshot.jpg`
- Default icon: `/assets/images/default-icon.jpg`

## Term Metadata (term_metas)

### Category Styling
- **`term_color`**: Color code for category badges (used at 50% opacity for backgrounds)
- **`description`**: Category description (loaded on homepage for categories)

### Taxonomies Used
- **`category`**: Tool categories (e.g., "AI Writing", "Image Generation")
- **`pricing`**: Pricing tiers (e.g., "Free", "Freemium", "Paid", "Free Trial")

## Page-Specific Metadata Usage

### Homepage (`/` - +page.svelte)
**Item Cards Display:**
- `description` for card descriptions (truncated to 3 lines)
- `thumbnail_url` for card images
- Category and pricing terms for badges
- `term_color` for category badge styling

**Featured Tools Sidebar:**
- `icon_url` for tool icons
- `description` for brief descriptions

### Tool Detail Page (`/ai/[slug]` - +page.svelte)
**Main Content:**
- `description` for page description and meta tags
- `screenshot_url` for main tool screenshot
- `content` or `description` for detailed "About" section
- `dofollow` controls whether external links are dofollow (1) or nofollow (0/other)
- Categories and pricing terms as badges

**Related Tools:**
- Same metadata as homepage cards

### Category Pages (`/category/[slug]` - +page.svelte)
**Tool Listings:**
- Same metadata pattern as homepage
- Filtered by specific category term

### Pricing Pages (`/pricing/[slug]` - +page.svelte)
**Tool Listings:**
- Same metadata pattern as homepage
- Filtered by specific pricing term

### Featured Page (`/featured` - +page.svelte)
**Featured Tools:**
- Only shows items with `item_status='featured'`
- Same metadata pattern as homepage

### Submit Page (`/submit` - +page.svelte)
**Form Fields Map to Metadata:**
- Tool Name → `item_name`
- Short Description → `description` (item_meta)
- About Tool → `content` (item_meta)
- Screenshot Upload → `screenshot_url` (item_meta)
- Icon Upload → `icon_url` (item_meta)
- Website URL → `item_url` (items table)

## Helper Functions Pattern

All pages use consistent helper functions:
```javascript
// Get metadata value from item
function getMetaValue(item, key) {
  const meta = item.metadata.find(m => m.item_meta_key === key);
  return meta ? meta.item_meta_value || '' : '';
}

// Get term by taxonomy
function getTermByTaxonomy(item, taxonomy) {
  return item.terms.find(t => t.term_taxonomy === taxonomy);
}
```

## Technology Stack Notes

- **Runtime**: Bun (instead of Node.js)
- **Language**: TypeScript
- **Framework**: SvelteKit with SSR
- **Database**: PostgreSQL with custom SQL queries
- **Image Processing**: Jimp (preferred over Sharp for Bun compatibility)
- **UI Components**: shadcn/svelte components

## API Patterns

- All API routes use POST method for consistency
- RPC-like naming convention (verb + object)
- Proper parameter validation with conditionals
- Server-side database operations only (never client-side)

## Routing Structure

- `/` - Homepage (home.html equivalent)
- `/submit` - Tool submission form (submit.html equivalent)
- `/ai/[slug]` - Individual tool pages (single.html equivalent)
- `/category/[slug]` - Category listing pages
- `/pricing/[slug]` - Pricing tier listing pages
- `/categories` - All categories overview (categories.html equivalent)
- `/featured` - Featured tools page
- `/new` - Newest tools (newest first)
- `/privacy` - Privacy policy (default-page.html equivalent)
- `/terms` - Terms of service (default-page.html equivalent)

## Important Implementation Notes

1. **No Pagination**: All listings show all available items
2. **No Breadcrumbs**: Pages don't include breadcrumb navigation
3. **SSR Required**: Directory pages use server-side rendering for SEO
4. **Mobile Responsive**: All components are mobile-first responsive
5. **Consistent Styling**: Main titles use consistent size, color, and margins
6. **Image Fallbacks**: Always provide fallback images for missing assets

## Development Setup

### Prerequisites
- Bun runtime (instead of Node.js)
- PostgreSQL database

### Getting Started
```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Build for production
bun run build
```

### Environment Configuration
Configure the following in your environment:
- `PUBLIC_SITE_DOMAIN`: Site domain for database naming and URLs
- Database connection settings

## Database Migration Notes

- Items table includes `item_url` column (migrated from `tool_site_url`)
- Use number type for IDs (not bigint) to avoid type issues
- All foreign key relationships require existing parent records
- Featured tools use `item_status='featured'`
