import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { searchItems } from '$lib/services/items';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    const query = body.query || '';
    const limit = body.limit || 50;
    const offset = body.offset || 0;

    // Validate required parameters
    if (!query || query.trim() === '') {
      return json(createErrorResponse('query is required'), { status: 400 });
    }

    const result = await searchItems(query, limit, offset);

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
