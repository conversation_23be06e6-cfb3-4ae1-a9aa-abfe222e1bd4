import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { deleteItem } from '$lib/services/items.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { DeleteItemRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: DeleteItemRequest = await request.json();
    const deleted = await deleteItem(body.item_id);
    
    return json(createSuccessResponse({ deleted }));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
