<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { authStore } from '$lib/stores/auth';
	import Head from '$lib/components/Head.svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';

	onMount(() => {
		authStore.logout();
		
		setTimeout(() => {
			goto('/');
		}, 2000);
	});
</script>

<Head
	title="Logged Out - {PUBLIC_SITE_NAME}"
	description="You have been successfully logged out."
	url="{PUBLIC_SITE_URL}/logout"
/>

<div class="min-h-screen flex items-center justify-center bg-gray-50">
	<div class="text-center">
		<div class="mb-8">
			<div class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
				<svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
				</svg>
			</div>
			<h1 class="text-2xl font-bold text-gray-900 mb-2">Successfully Logged Out</h1>
			<p class="text-gray-600">You have been logged out of your account.</p>
		</div>
		
		<div class="space-y-4">
			<p class="text-sm text-gray-500">Redirecting you to the homepage...</p>
			<div class="flex justify-center">
				<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
			</div>
		</div>
		
		<div class="mt-8">
			<a href="/" class="text-blue-600 hover:text-blue-800 font-medium">
				Go to Homepage
			</a>
		</div>
	</div>
</div>
