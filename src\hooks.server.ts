import type { Handle } from '@sveltejs/kit';
import { verifyUserToken } from '$lib/services/auth.js';

export const handle: Handle = async ({ event, resolve }) => {
  const token = event.cookies.get('auth_token');
  
  if (token) {
    const userPayload = verifyUserToken(token);
    if (userPayload) {
      event.locals.user = {
        user_id: userPayload.user_id,
        user_firstname: userPayload.user_firstname,
        user_email: userPayload.user_email,
        user_type: userPayload.user_type
      };
    } else {
      event.cookies.delete('auth_token', { path: '/' });
    }
  }

  return resolve(event);
};
