import { v4 as uuidv4 } from 'uuid';
import { mkdir, writeFile } from 'fs/promises';
import { join } from 'path';

export interface ImageProcessingResult {
  filename: string;
  url: string;
  thumbnailFilename?: string;
  thumbnailUrl?: string;
}

export async function processScreenshot(file: File, siteUrl: string): Promise<ImageProcessingResult> {
  // Validate file size (max 2MB)
  if (file.size > 2 * 1024 * 1024) {
    throw new Error('Screenshot file size must be less than 2MB');
  }

  // Generate UUID filename
  const uuid = uuidv4();
  const filename = `${uuid}.jpg`;
  const thumbnailFilename = `${uuid}_thumb.jpg`;

  // Ensure images directory exists
  const imagesDir = join(process.cwd(), 'static', 'images');
  try {
    await mkdir(imagesDir, { recursive: true });
  } catch (error) {
    // Directory might already exist, that's fine
    console.log('Images directory already exists or created');
  }

  // Convert file to buffer
  const buffer = Buffer.from(await file.arrayBuffer());

  // Dynamic import for Jimp
  const { Jimp } = await import('jimp');

  // Process main screenshot
  const image = await Jimp.read(buffer);

  // Resize if larger than 1600x900
  if (image.bitmap.width > 1600) {
    image.scaleToFit({ w: 1600, h: 900 });
  }

  // Convert to JPEG and save
  const screenshotPath = join(imagesDir, filename);
  const screenshotBuffer = await image.getBuffer('image/jpeg', { quality: 50 });
  await writeFile(screenshotPath, screenshotBuffer);

  // Create thumbnail (400x225)
  const thumbnailImage = await Jimp.read(buffer);
  thumbnailImage.cover({ w: 400, h: 225 });
  const thumbnailPath = join(imagesDir, thumbnailFilename);
  const thumbnailBuffer = await thumbnailImage.getBuffer('image/jpeg', { quality: 50 });
  await writeFile(thumbnailPath, thumbnailBuffer);

  return {
    filename,
    url: `${siteUrl}/images/${filename}`,
    thumbnailFilename,
    thumbnailUrl: `${siteUrl}/images/${thumbnailFilename}`
  };
}

export async function processIcon(file: File, siteUrl: string): Promise<ImageProcessingResult> {
  // Generate UUID filename
  const uuid = uuidv4();
  const filename = `${uuid}_icon.jpg`;

  // Ensure images directory exists
  const imagesDir = join(process.cwd(), 'static', 'images');
  try {
    await mkdir(imagesDir, { recursive: true });
  } catch (error) {
    // Directory might already exist, that's fine
    console.log('Images directory already exists or created');
  }

  // Convert file to buffer
  const buffer = Buffer.from(await file.arrayBuffer());

  // Dynamic import for Jimp
  const { Jimp } = await import('jimp');

  // Process icon
  const image = await Jimp.read(buffer);

  // Resize if larger than 500x500
  if (image.bitmap.width > 500 || image.bitmap.height > 500) {
    image.scaleToFit({ w: 500, h: 500 });
  }

  // Convert to JPEG and save
  const iconPath = join(imagesDir, filename);
  const iconBuffer = await image.getBuffer('image/jpeg', { quality: 50 });
  await writeFile(iconPath, iconBuffer);

  return {
    filename,
    url: `${siteUrl}/images/${filename}`
  };
}
