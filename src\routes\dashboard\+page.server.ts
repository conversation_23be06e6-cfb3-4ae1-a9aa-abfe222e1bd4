import { getItemsByStatus, getItemsWithMetadata } from '$lib/services/items.js';
import type { PageServerLoad } from './$types';
import sql from '$lib/db/db.js';

export const load: PageServerLoad = async ({ url }) => {
	try {
		// Get all pending items
		const { items, total } = await getItemsByStatus(0, 1000, 0);

		// Get metadata for these items
		const itemIds = items.map((item: any) => item.item_id);
		let metadata: any[] = [];
		if (itemIds.length > 0) {
			metadata = await sql`
				SELECT item_id, item_meta_key, item_meta_value
				FROM item_metas
				WHERE item_id = ANY(${itemIds})
			`;
		}

		// Enrich items with metadata
		const enrichedItems = items.map((item: any) => {
			const itemMetadata = metadata.filter((m: any) => m.item_id === item.item_id);
			return {
				...item,
				metadata: itemMetadata
			};
		});

		// Get featured items for management
		const { items: featuredItems } = await getItemsWithMetadata('featured', 100, 0);

		// Get all active items for adding to featured
		const { items: activeItems } = await getItemsWithMetadata('active', 1000, 0);

		return {
			items: enrichedItems,
			total,
			featuredItems,
			activeItems
		};
	} catch (error) {
		console.error('Error loading dashboard data:', error);
		return {
			items: [],
			total: 0,
			featuredItems: [],
			activeItems: []
		};
	}
};
