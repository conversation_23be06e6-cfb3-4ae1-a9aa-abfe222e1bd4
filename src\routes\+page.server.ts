import { getItemsWithMetadata } from '$lib/services/items.js';
import { getTermsWithMetas } from '$lib/services/terms.js';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async () => {
	try {
		console.log('Loading homepage data');

		// Use optimized functions - now only 3 queries instead of ~10
		const [
			{ items: allItems },
			enrichedCategories,
			{ items: featuredItems }
		] = await Promise.all([
			getItemsWithMetadata('active', 20, 0),
			getTermsWithMetas('category'),
			getItemsWithMetadata('featured', 4, 0)
		]);

		console.log('Loaded items with metadata:', allItems.length);
		console.log('Loaded categories:', enrichedCategories.length);
		console.log('Loaded featured items:', featuredItems.length);

		return {
			featuredItems,
			items: allItems,
			categories: enrichedCategories
		};
	} catch (error) {
		console.error('Error loading homepage data:', error);
		return {
			featuredItems: [],
			items: [],
			categories: []
		};
	}
};
