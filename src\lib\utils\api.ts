import { verifyMasterToken } from '$lib/services/auth.js';
import type { ApiResponse } from '$lib/types/responses.js';

export function authenticateRequest(request: Request): boolean {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return !!verifyMasterToken(token);
}

export function createUnauthorizedResponse<T>(): ApiResponse<T> {
  return {
    success: false,
    error: 'Unauthorized'
  };
}

export function createErrorResponse<T>(error: unknown): ApiResponse<T> {
  return {
    success: false,
    error: error instanceof Error ? error.message : 'Unknown error'
  };
}

export function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data
  };
}

// Validation utilities
export function validatePositiveInteger(value: string | null, fieldName: string): { isValid: boolean; value?: number; error?: string } {
  if (value === null || value.trim() === '') {
    return { isValid: true }; // Optional parameter
  }

  const parsed = parseInt(value);
  if (isNaN(parsed) || parsed <= 0) {
    return {
      isValid: false,
      error: `Invalid ${fieldName} parameter. Must be a positive integer.`
    };
  }

  return { isValid: true, value: parsed };
}

export function validateNonNegativeInteger(value: string | null, fieldName: string): { isValid: boolean; value?: number; error?: string } {
  if (value === null || value.trim() === '') {
    return { isValid: true }; // Optional parameter
  }

  const parsed = parseInt(value);
  if (isNaN(parsed) || parsed < 0) {
    return {
      isValid: false,
      error: `Invalid ${fieldName} parameter. Must be a non-negative integer.`
    };
  }

  return { isValid: true, value: parsed };
}

export function validateNumber(value: string | null, fieldName: string): { isValid: boolean; value?: number; error?: string } {
  if (value === null || value.trim() === '') {
    return { isValid: true }; // Optional parameter
  }

  try {
    const parsed = parseInt(value);
    if (isNaN(parsed) || parsed <= 0) {
      return {
        isValid: false,
        error: `Invalid ${fieldName} parameter. Must be a positive integer.`
      };
    }
    return { isValid: true, value: parsed };
  } catch {
    return {
      isValid: false,
      error: `Invalid ${fieldName} parameter. Must be a valid integer.`
    };
  }
}

export function validateLimit(value: string | null): { isValid: boolean; value: number; error?: string } {
  const defaultLimit = 50;

  if (value === null || value.trim() === '') {
    return { isValid: true, value: defaultLimit };
  }

  const parsed = parseInt(value);
  if (isNaN(parsed) || parsed <= 0 || parsed > 1000) {
    return {
      isValid: false,
      value: defaultLimit,
      error: 'Invalid limit parameter. Must be between 1 and 1000.'
    };
  }

  return { isValid: true, value: parsed };
}

export function validateOffset(value: string | null): { isValid: boolean; value: number; error?: string } {
  const defaultOffset = 0;

  if (value === null || value.trim() === '') {
    return { isValid: true, value: defaultOffset };
  }

  const parsed = parseInt(value);
  if (isNaN(parsed) || parsed < 0) {
    return {
      isValid: false,
      value: defaultOffset,
      error: 'Invalid offset parameter. Must be a non-negative integer.'
    };
  }

  return { isValid: true, value: parsed };
}

export function validateRequiredString(value: any, fieldName: string): { isValid: boolean; value?: string; error?: string } {
  if (value === undefined || value === null) {
    return {
      isValid: false,
      error: `${fieldName} is required`
    };
  }

  if (typeof value !== 'string') {
    return {
      isValid: false,
      error: `${fieldName} must be a string`
    };
  }

  const trimmed = value.trim();
  if (trimmed === '') {
    return {
      isValid: false,
      error: `${fieldName} cannot be empty`
    };
  }

  return { isValid: true, value: trimmed };
}

export function validateRequiredPositiveInteger(value: any, fieldName: string): { isValid: boolean; value?: number; error?: string } {
  if (value === undefined || value === null) {
    return {
      isValid: false,
      error: `${fieldName} is required`
    };
  }

  if (typeof value !== 'number' || value <= 0) {
    return {
      isValid: false,
      error: `${fieldName} must be a positive integer`
    };
  }

  return { isValid: true, value };
}

export function validateRequiredNumber(value: any, fieldName: string): { isValid: boolean; value?: number; error?: string } {
  if (value === undefined || value === null) {
    return {
      isValid: false,
      error: `${fieldName} is required`
    };
  }

  // Handle number, bigint, and string types
  let numberValue: number;
  try {
    if (typeof value === 'number') {
      numberValue = value;
    } else if (typeof value === 'bigint') {
      numberValue = Number(value);
    } else if (typeof value === 'string') {
      numberValue = parseInt(value);
    } else {
      return {
        isValid: false,
        error: `${fieldName} must be a valid integer`
      };
    }

    if (isNaN(numberValue) || numberValue <= 0) {
      return {
        isValid: false,
        error: `${fieldName} must be a positive integer`
      };
    }

    return { isValid: true, value: numberValue };
  } catch {
    return {
      isValid: false,
      error: `${fieldName} must be a valid integer`
    };
  }
}
