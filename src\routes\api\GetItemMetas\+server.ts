import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getItemMetas, searchItemMetas } from '$lib/services/item_metas.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    const item_id = body.item_id;
    const limit = body.limit || 50;
    const offset = body.offset || 0;
    const search = body.search || '';

    let result;

    // If there's a search query, use search function
    if (search && search.trim().length >= 3) {
      result = await searchItemMetas(search.trim(), limit, offset);
    }
    // Otherwise, use regular getItemMetas
    else {
      result = await getItemMetas(item_id, limit, offset);
    }

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
