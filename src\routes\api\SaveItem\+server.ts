import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { saveItem } from '$lib/services/items.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { SaveItemRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: SaveItemRequest = await request.json();
    const item = await saveItem(body.item);

    return json(createSuccessResponse(item));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
