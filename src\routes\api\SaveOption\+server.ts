import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { saveOption } from '$lib/services/options.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { SaveOptionRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const body: SaveOptionRequest = await request.json();
    const option = await saveOption(body.option);

    return json(createSuccessResponse(option));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
