import { getActiveItemsByTermWithMetadata } from '$lib/services/items.js';
import { getTerms } from '$lib/services/terms.js';
import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ params, url }) => {
	try {
		const slug = params.slug;

		// Get page from URL params
		const page = parseInt(url.searchParams.get('page') || '1');
		const limit = 40;
		const offset = (page - 1) * limit;

		// Get the category term first to verify it exists
		const terms = await getTerms('category');
		const category = terms.find((t: any) => t.term_slug === slug);
		
		if (!category) {
			throw error(404, 'Category not found');
		}
		
		// Get items for this category (only active or featured, with metadata)
		const { items, total } = await getActiveItemsByTermWithMetadata(slug, 'category', limit, offset);

		// Calculate pagination info
		const totalPages = Math.ceil(total / limit);
		const hasNextPage = page < totalPages;
		const hasPrevPage = page > 1;

		return {
			category,
			items,
			total,
			currentPage: page,
			totalPages,
			hasNextPage,
			hasPrevPage
		};
	} catch (err) {
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		console.error('Error loading category data:', err);
		throw error(500, 'Failed to load category data');
	}
};
