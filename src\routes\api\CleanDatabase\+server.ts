import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { cleanDatabase } from '$lib/services/migration.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const result = await cleanDatabase();
    
    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
