import { readdir } from 'fs/promises';
import { join } from 'path';
import Database from 'better-sqlite3';

export async function getSqliteFiles(): Promise<string[]> {
  try {
    const dbsPath = join(process.cwd(), 'dbs');
    const files = await readdir(dbsPath);
    return files.filter(file => file.endsWith('.db') && !file.includes('-shm') && !file.includes('-wal'));
  } catch (error) {
    console.error('Error reading dbs folder:', error);
    return [];
  }
}

export async function analyzeDatabase(sqlite_file: string): Promise<{
  total_users: number;
  admin_users: number;
  member_users: number;
  total_tools: number;
  admin_tools: number;
  member_tools: number;
  total_relationships: number;
  admin_relationships: number;
}> {
  try {
    const dbPath = join(process.cwd(), 'dbs', sqlite_file);
    console.log('Opening SQLite database for analysis:', dbPath);
    const sqliteDb = new Database(dbPath, { readonly: true });
    console.log('SQLite database opened successfully');

    // Count users by role
    console.log('Counting users...');
    const totalUsers = sqliteDb.prepare('SELECT COUNT(*) as count FROM users').get() as any;
    console.log('Total users:', totalUsers.count);

    // Note: SQLite still uses old schema with user_role (1=admin, 0=member)
    const adminUsers = sqliteDb.prepare('SELECT COUNT(*) as count FROM users WHERE user_role = 1').get() as any;
    console.log('Admin users:', adminUsers.count);

    const memberUsers = sqliteDb.prepare('SELECT COUNT(*) as count FROM users WHERE user_role = 0').get() as any;
    console.log('Member users:', memberUsers.count);

    // Count tools by user role
    console.log('Counting tools...');
    const totalTools = sqliteDb.prepare('SELECT COUNT(*) as count FROM tools').get() as any;
    console.log('Total tools:', totalTools.count);

    const adminTools = sqliteDb.prepare(`
      SELECT COUNT(*) as count
      FROM tools t
      JOIN users u ON t.user_id = u.user_id
      WHERE u.user_role = 1
    `).get() as any;
    console.log('Admin tools:', adminTools.count);

    const memberTools = sqliteDb.prepare(`
      SELECT COUNT(*) as count
      FROM tools t
      JOIN users u ON t.user_id = u.user_id
      WHERE u.user_role = 0
    `).get() as any;
    console.log('Member tools:', memberTools.count);

    // Count relationships for admin tools
    console.log('Counting relationships...');
    const totalRelationships = sqliteDb.prepare('SELECT COUNT(*) as count FROM tool_term_relationships').get() as any;
    console.log('Total relationships:', totalRelationships.count);

    const adminRelationships = sqliteDb.prepare(`
      SELECT COUNT(*) as count
      FROM tool_term_relationships ttr
      JOIN tools t ON ttr.tool_id = t.tool_id
      JOIN users u ON t.user_id = u.user_id
      WHERE u.user_role = 1
    `).get() as any;
    console.log('Admin relationships:', adminRelationships.count);

    console.log('Closing SQLite database...');
    sqliteDb.close();
    console.log('SQLite database closed successfully');

    return {
      total_users: totalUsers.count,
      admin_users: adminUsers.count,
      member_users: memberUsers.count,
      total_tools: totalTools.count,
      admin_tools: adminTools.count,
      member_tools: memberTools.count,
      total_relationships: totalRelationships.count,
      admin_relationships: adminRelationships.count
    };
  } catch (error) {
    console.error('Error analyzing database:', error);
    throw new Error(`Failed to analyze database: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function removeAdminTools(sqlite_file: string): Promise<{
  success: boolean;
  message: string;
  stats: {
    tools_removed: number;
    relationships_removed: number;
  }
}> {
  const stats = {
    tools_removed: 0,
    relationships_removed: 0
  };

  try {
    const dbPath = join(process.cwd(), 'dbs', sqlite_file);
    console.log('Opening SQLite database for modification:', dbPath);
    const sqliteDb = new Database(dbPath, { readonly: false });
    console.log('SQLite database opened successfully for modification');

    console.log(`Starting admin tools removal from ${sqlite_file}`);

    // First, get the list of admin tool IDs
    const adminToolIds = sqliteDb.prepare(`
      SELECT t.tool_id 
      FROM tools t 
      JOIN users u ON t.user_id = u.user_id 
      WHERE u.user_role = 1
    `).all() as any[];

    console.log(`Found ${adminToolIds.length} admin tools to remove`);

    if (adminToolIds.length > 0) {
      // Start transaction
      const transaction = sqliteDb.transaction(() => {
        // Remove relationships for admin tools
        for (const tool of adminToolIds) {
          const relationshipsRemoved = sqliteDb.prepare(`
            DELETE FROM tool_term_relationships WHERE tool_id = ?
          `).run(tool.tool_id);
          stats.relationships_removed += relationshipsRemoved.changes;
        }

        // Remove admin tools
        const toolsRemoved = sqliteDb.prepare(`
          DELETE FROM tools 
          WHERE tool_id IN (
            SELECT t.tool_id 
            FROM tools t 
            JOIN users u ON t.user_id = u.user_id 
            WHERE u.user_role = 1
          )
        `).run();
        stats.tools_removed = toolsRemoved.changes;
      });

      transaction();
      console.log(`Removed ${stats.tools_removed} admin tools and ${stats.relationships_removed} relationships`);
    }

    sqliteDb.close();

    return {
      success: true,
      message: `Successfully removed admin tools from ${sqlite_file}`,
      stats
    };

  } catch (error) {
    console.error('Error removing admin tools:', error);
    throw new Error(`Failed to remove admin tools: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
