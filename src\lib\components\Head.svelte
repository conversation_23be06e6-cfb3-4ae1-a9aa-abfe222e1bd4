<script>
  import { PUBLIC_SITE_URL, PUBLIC_SITE_NAME, PUBLIC_SITE_OGIMAGE } from '$lib/utils/env';
  let { title, description, url } = $props();
</script>

<svelte:head>
  <title>{ title }</title>
  
  <meta property="og:title" content={ title } />
  <meta property="og:description" content={ description } />
  <meta property="og:image" content={ PUBLIC_SITE_OGIMAGE } />
  <meta property="og:url" content={ url } />
  <meta property="og:type" content="website" />
  <meta property="og:locale" content="en_US" />
  <meta property="og:site_name" content={ PUBLIC_SITE_NAME } />
  
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:site" content="@" />
  <meta name="twitter:title" content={ title } />
  <meta name="twitter:description" content={ description } />
  <meta name="twitter:image" content={ PUBLIC_SITE_OGIMAGE } />
  
  <meta name="description" content={ description } />
  
  <link rel="canonical" href={ url } />
  
  <link rel="apple-touch-icon" sizes="60x60" href={ PUBLIC_SITE_URL + "/assets/icons/icon-60x60.png" } />
  <link rel="apple-touch-icon" sizes="120x120" href={ PUBLIC_SITE_URL + "/assets/icons/icon-120x120.png" } />
  <link rel="apple-touch-icon" sizes="180x180" href={ PUBLIC_SITE_URL + "/assets/icons/icon-180x180.png" } />
  
  <link rel="icon" type="image/png" sizes="192x192" href={ PUBLIC_SITE_URL + "/assets/icons/icon-192x192.png" } />
  <link rel="icon" type="image/png" sizes="96x96" href={ PUBLIC_SITE_URL + "/assets/icons/icon-96x96.png" } />
  <link rel="icon" type="image/png" sizes="32x32" href={ PUBLIC_SITE_URL + "/assets/icons/icon-32x32.png" } />
  <link rel="icon" type="image/png" sizes="16x16" href={ PUBLIC_SITE_URL + "/assets/icons/icon-16x16.png" } />
  
  <link rel="manifest" href={ PUBLIC_SITE_URL + "/site.webmanifest" } />
  <link rel="robots" href={ PUBLIC_SITE_URL + "/robots.txt" } type="text/plain">
  <meta name="msapplication-TileColor" content="#ffffff" />
  <meta name="msapplication-TileImage" content={ PUBLIC_SITE_URL + "/assets/icons/icon-144x144.png" } />
  <meta name="theme-color" content="#0000ff" />
</svelte:head>
