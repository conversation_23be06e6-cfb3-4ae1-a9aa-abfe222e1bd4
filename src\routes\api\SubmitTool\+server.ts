import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { PUBLIC_SITE_DOMAIN } from '$lib/utils/env.js';
import sql from '$lib/db/db.js';
import { processScreenshot, processIcon } from '$lib/utils/image.js';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const formData = await request.formData();
		
		// Extract form data
		const websiteUrl = formData.get('websiteUrl') as string;
		const toolName = formData.get('toolName') as string;
		const shortDescription = formData.get('shortDescription') as string;
		const aboutTool = formData.get('aboutTool') as string;
		const selectedCategoriesStr = formData.get('selectedCategories') as string;
		const selectedPricing = formData.get('selectedPricing') as string;
		const screenshotFile = formData.get('screenshotFile') as File;
		const iconFile = formData.get('iconFile') as File;

		// Parse selected categories
		let selectedCategories: string[] = [];
		try {
			selectedCategories = JSON.parse(selectedCategoriesStr || '[]');
		} catch (error) {
			return json({ error: 'Invalid categories format' }, { status: 400 });
		}

		// Validate required fields
		if (!websiteUrl || !toolName || !shortDescription || !aboutTool || selectedCategories.length === 0) {
			return json({ error: 'Missing required fields' }, { status: 400 });
		}
		
		// Validate short description length
		if (shortDescription.length > 150) {
			return json({ error: 'Short description must be 150 characters or less' }, { status: 400 });
		}

		const siteUrl = `https://${PUBLIC_SITE_DOMAIN}`;
		const currentTimestamp = Math.floor(Date.now() / 1000);

		// Create slug from tool name
		const slug = toolName.toLowerCase()
			.replace(/[^a-z0-9\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.trim();

		// Process images
		let screenshotResult = null;
		let iconResult = null;

		try {
			if (screenshotFile && screenshotFile.size > 0) {
				screenshotResult = await processScreenshot(screenshotFile, siteUrl);
			}

			if (iconFile && iconFile.size > 0) {
				iconResult = await processIcon(iconFile, siteUrl);
			}
		} catch (error) {
			console.error('Image processing error:', error);
			return json({ error: error instanceof Error ? error.message : 'Image processing failed' }, { status: 400 });
		}

		// Insert item into database
		const [newItem] = await sql`
			INSERT INTO items (
				item_name,
				item_slug,
				item_url,
				item_status,
				item_created_at,
				user_id
			) VALUES (
				${toolName},
				${slug},
				${websiteUrl},
				0,
				${currentTimestamp},
				1
			) RETURNING item_id
		`;

		const itemId = newItem.item_id;

		// Insert metadata
		const metadataInserts = [
			{ key: 'description', value: shortDescription },
			{ key: 'content', value: aboutTool }
		];

		// Add image metadata
		if (screenshotResult) {
			metadataInserts.push({ key: 'screenshot_url', value: screenshotResult.url });
			if (screenshotResult.thumbnailUrl) {
				metadataInserts.push({ key: 'thumbnail_url', value: screenshotResult.thumbnailUrl });
			}
		}

		if (iconResult) {
			metadataInserts.push({ key: 'icon_url', value: iconResult.url });
		}
		
		// Insert all metadata
		for (const meta of metadataInserts) {
			await sql`
				INSERT INTO item_metas (item_id, item_meta_key, item_meta_value)
				VALUES (${itemId}, ${meta.key}, ${meta.value})
			`;
		}
		
		// Get term IDs for categories and pricing
		// Handle multiple categories
		for (const categorySlug of selectedCategories) {
			const categoryTerm = await sql`
				SELECT term_id FROM terms
				WHERE term_slug = ${categorySlug} AND term_taxonomy = 'category'
				LIMIT 1
			`;

			if (categoryTerm.length > 0) {
				await sql`
					INSERT INTO item_term_relationships (item_id, term_id)
					VALUES (${itemId}, ${categoryTerm[0].term_id})
				`;
			}
		}
		
		if (selectedPricing) {
			const pricingTerm = await sql`
				SELECT term_id FROM terms
				WHERE term_slug = ${selectedPricing} AND term_taxonomy = 'pricing'
				LIMIT 1
			`;
			
			if (pricingTerm.length > 0) {
				await sql`
					INSERT INTO item_term_relationships (item_id, term_id)
					VALUES (${itemId}, ${pricingTerm[0].term_id})
				`;
			}
		}
		
		return json({ 
			success: true, 
			message: 'Tool submitted successfully!',
			itemId: itemId
		});
		
	} catch (error) {
		console.error('Error submitting tool:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
