import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { getTerms } from '$lib/services/terms.js';
import { createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    // Parse request body for parameters
    let body: any = {};
    try {
      body = await request.json();
    } catch {
      // If no body or invalid JSON, use defaults
    }

    const term_taxonomy = body.term_taxonomy;
    // Note: getTerms doesn't support limit/offset parameters
    // If pagination is needed, it should be handled after getting all terms

    const result = await getTerms(term_taxonomy);

    return json(createSuccessResponse(result));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
