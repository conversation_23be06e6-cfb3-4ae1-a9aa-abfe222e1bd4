import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { saveTermMeta } from '$lib/services/term_metas.js';
import { authenticateRequest, createUnauthorizedResponse, createErrorResponse, createSuccessResponse } from '$lib/utils/api.js';
import type { SaveTermMetaRequest } from '$lib/types/requests.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    if (!authenticateRequest(request)) {
      return json(createUnauthorizedResponse(), { status: 401 });
    }
    
    const body: SaveTermMetaRequest = await request.json();
    const termMeta = await saveTermMeta(body.term_meta);
    
    return json(createSuccessResponse(termMeta));
  } catch (error) {
    return json(createErrorResponse(error), { status: 500 });
  }
};
