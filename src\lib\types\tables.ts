export interface User {
  user_id?: number;
  user_firstname: string;
  user_email: string;
  user_created_at: number;
  user_type: number;
}

export interface Item {
  item_id?: number;
  item_name: string;
  item_slug: string;
  item_url: string;
  item_status: number;
  item_created_at: number;
  user_id: number;
}

export interface Term {
  term_id?: number;
  term_name: string;
  term_slug: string;
  term_taxonomy: string;
}

export interface ItemTermRelationship {
  item_id: number;
  term_id: number;
}

export interface ItemMeta {
  item_meta_id?: number;
  item_id: number;
  item_meta_key: string;
  item_meta_value?: string;
}

export interface TermMeta {
  term_meta_id?: number;
  term_id: number;
  term_meta_key: string;
  term_meta_value?: string;
}

export interface Option {
  option_id?: number;
  option_key: string;
  option_value?: string;
}